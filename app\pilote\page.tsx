"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Building2,
  CheckSquare,
  Users,
  Plus,
  Activity,
  Clock,
} from "lucide-react";

interface PiloteStats {
  totalSegments: number;
  totalTasks: number;
  totalResponsables: number;
  recentActivity: {
    type: string;
    message: string;
    time: string;
    user?: string;
  }[];
  recentSegments: {
    id: string;
    title: string;
    tasksCount: number;
    responsablesCount: number;
    progress: number;
    color: string;
  }[];
  taskStats: {
    total: number;
    completed: number;
    inProgress: number;
  };
}

export default function PiloteDashboard() {
  const [stats, setStats] = useState<PiloteStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchPiloteStats();
  }, []);

  const fetchPiloteStats = async () => {
    try {
      const response = await fetch("/api/pilote/dashboard-stats");
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["PILOTE_CELLULE"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["PILOTE_CELLULE"]}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Tableau de bord Pilote
            </h1>
            <p className="text-muted-foreground">
              Gérez vos segments et supervisez l'avancement des tâches
            </p>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Mes Segments
              </CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalSegments || 0}
              </div>
              <p className="text-xs text-muted-foreground">Segments créés</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Tâches Créées
              </CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalTasks || 0}</div>
              <p className="text-xs text-muted-foreground">Tâches créées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Responsables Assignés
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.totalResponsables || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Responsables assignés
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Activité Récente des Responsables</span>
              </CardTitle>
              <CardDescription>
                Dernières actions de vos responsables
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.recentActivity && stats.recentActivity.length > 0 ? (
                  stats.recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div
                        className={`w-2 h-2 rounded-full mt-2 ${
                          activity.type === "task_completed"
                            ? "bg-green-500"
                            : activity.type === "kpi_added"
                            ? "bg-blue-500"
                            : activity.type === "task_reopened"
                            ? "bg-orange-500"
                            : "bg-gray-500"
                        }`}
                      ></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {activity.message}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{activity.time}</span>
                          {activity.user && <span>• {activity.user}</span>}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Aucune activité récente
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Segments Récents</CardTitle>
              <CardDescription>Vos derniers segments créés</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.recentSegments && stats.recentSegments.length > 0 ? (
                  stats.recentSegments.map((segment) => (
                    <div
                      key={segment.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: segment.color }}
                        ></div>
                        <div>
                          <p className="font-medium">{segment.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {segment.tasksCount} tâches •{" "}
                            {segment.responsablesCount} responsables
                          </p>
                        </div>
                      </div>
                      <div className="text-sm text-green-600 font-medium">
                        {segment.progress}%
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Aucun segment créé pour le moment
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Progression des Tâches</CardTitle>
            <CardDescription>
              Vue d'ensemble de l'avancement global
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.taskStats ? (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Tâches terminées</span>
                    <span className="text-sm font-medium text-green-600">
                      {stats.taskStats.completed}/{stats.taskStats.total}
                    </span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-3">
                    <div
                      className="bg-green-600 h-3 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          stats.taskStats.total > 0
                            ? (stats.taskStats.completed /
                                stats.taskStats.total) *
                              100
                            : 0
                        }%`,
                      }}
                    ></div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 mt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {stats.taskStats.inProgress}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        En cours
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {stats.taskStats.completed}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Terminées
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {stats.taskStats.total}
                      </div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                  </div>
                </>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Aucune tâche créée pour le moment
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
