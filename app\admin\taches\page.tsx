"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  CheckSquare,
  Plus,
  Edit,
  Trash2,
  <PERSON>,
  Clock,
  AlertCircle,
  Eye,
} from "lucide-react";

interface Segment {
  id: string;
  title: string;
  cellule: {
    title: string;
  };
}

interface User {
  id: string;
  name: string;
  email: string;
}

interface Task {
  id: string;
  title: string;
  description: string;
  color: string;
  status: "FAIT" | "NON_FAIT";
  priority: "URGENT" | "NORMAL" | "NOT_IMPORTANT";
  createdAt: string;
  segment: {
    id: string;
    title: string;
    cellule: {
      title: string;
    };
  };
  createdBy: {
    name: string;
    email: string;
  };
  assignees: {
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
  kpis: {
    id: string;
    title: string;
    description: string;
    value: number;
    createdBy: {
      name: string;
    };
  }[];
}

const colorOptions = [
  { value: "#3B82F6", label: "Bleu", class: "bg-blue-500" },
  { value: "#8B5CF6", label: "Violet", class: "bg-violet-500" },
  { value: "#06B6D4", label: "Cyan", class: "bg-cyan-500" },
  { value: "#10B981", label: "Vert", class: "bg-emerald-500" },
  { value: "#F59E0B", label: "Orange", class: "bg-amber-500" },
  { value: "#EF4444", label: "Rouge", class: "bg-red-500" },
];

const priorityOptions = [
  { value: "URGENT", label: "Urgent" },
  { value: "NORMAL", label: "Normal" },
  { value: "NOT_IMPORTANT", label: "Faible" },
];

const priorityColors = {
  URGENT: "bg-red-100 text-red-800",
  NORMAL: "bg-blue-100 text-blue-800",
  NOT_IMPORTANT: "bg-gray-100 text-gray-800",
};

export default function AdminTachesPage() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [segments, setSegments] = useState<Segment[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState("#3B82F6");
  const [priority, setPriority] = useState("NORMAL");
  const [selectedSegmentId, setSelectedSegmentId] = useState("");
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);

  useEffect(() => {
    fetchTasks();
    fetchSegments();
    fetchUsers();
  }, []);

  const fetchTasks = async () => {
    try {
      const response = await fetch("/api/admin/tasks");
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks);
      }
    } catch (error) {
      setError("Erreur lors du chargement des tâches");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSegments = async () => {
    try {
      const response = await fetch("/api/admin/segments");
      if (response.ok) {
        const data = await response.json();
        setSegments(data.segments);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des segments");
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/admin/users");
      if (response.ok) {
        const data = await response.json();
        const responsableUsers = data.users.filter(
          (user: any) => user.role === "RESPONSABLE_TACHE"
        );
        console.log("Responsable users found:", responsableUsers);
        setUsers(responsableUsers);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs");
    }
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setColor("#3B82F6");
    setPriority("NORMAL");
    setSelectedSegmentId("");
    setSelectedUserIds([]);
    setEditingTask(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");

    // Validation côté client
    if (!title.trim()) {
      setError("Le titre est requis");
      return;
    }

    if (!selectedSegmentId) {
      setError("Veuillez sélectionner un segment");
      return;
    }

    const taskData = {
      title: title.trim(),
      description: description.trim(),
      color,
      priority,
      segmentId: selectedSegmentId,
      assigneeIds: selectedUserIds,
    };

    console.log("Sending task data:", taskData);

    try {
      const url = editingTask
        ? `/api/admin/tasks/${editingTask.id}`
        : "/api/admin/tasks";

      const method = editingTask ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(taskData),
      });

      if (response.ok) {
        setMessage(
          editingTask
            ? "Tâche mise à jour avec succès"
            : "Tâche créée avec succès"
        );
        setIsDialogOpen(false);
        resetForm();
        fetchTasks();
      } else {
        const data = await response.json();
        console.error("API Error:", data);
        setError(
          data.details
            ? `${data.error}: ${data.details}`
            : data.error || "Erreur lors de la sauvegarde"
        );
      }
    } catch (error) {
      console.error("Request Error:", error);
      setError(
        "Une erreur est survenue: " +
          (error instanceof Error ? error.message : String(error))
      );
    }
  };

  const handleEdit = (task: Task) => {
    setEditingTask(task);
    setTitle(task.title);
    setDescription(task.description);
    setColor(task.color);
    setPriority(task.priority);
    setSelectedSegmentId(task.segment.id);
    setSelectedUserIds(task.assignees.map((assignee) => assignee.user.id));
    setIsDialogOpen(true);
  };

  const handleDelete = async (taskId: string) => {
    if (
      !confirm(
        "Êtes-vous sûr de vouloir supprimer cette tâche ? Tous les KPIs associés seront également supprimés."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/tasks/${taskId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setMessage("Tâche supprimée avec succès");
        fetchTasks();
      } else {
        setError("Erreur lors de la suppression");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const handleUserSelection = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUserIds([...selectedUserIds, userId]);
    } else {
      setSelectedUserIds(selectedUserIds.filter((id) => id !== userId));
    }
  };

  const openCreateDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const openDetailsDialog = (task: Task) => {
    setSelectedTask(task);
    setIsDetailsOpen(true);
  };

  const getStatusBadge = (status: string) => {
    return status === "FAIT" ? (
      <Badge className="bg-green-100 text-green-800">Terminée</Badge>
    ) : (
      <Badge variant="secondary">En cours</Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    return (
      <Badge
        className={priorityColors[priority as keyof typeof priorityColors]}
      >
        {priorityOptions.find((p) => p.value === priority)?.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["ADMIN"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["ADMIN"]}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Gestion des Tâches
            </h1>
            <p className="text-muted-foreground">
              Vue d'ensemble et gestion de toutes les tâches de la plateforme
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Tâche
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingTask
                    ? "Modifier la tâche"
                    : "Créer une nouvelle tâche"}
                </DialogTitle>
                <DialogDescription>
                  {editingTask
                    ? "Modifiez les informations de cette tâche"
                    : "Ajoutez une nouvelle tâche et assignez-la aux responsables"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre de la tâche</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Ex: Développer l'interface utilisateur"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Description détaillée de la tâche"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="segment">Segment</Label>
                  <Select
                    value={selectedSegmentId}
                    onValueChange={setSelectedSegmentId}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un segment" />
                    </SelectTrigger>
                    <SelectContent>
                      {segments.map((segment) => (
                        <SelectItem key={segment.id} value={segment.id}>
                          {segment.title} - {segment.cellule.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Couleur</Label>
                  <Select value={color} onValueChange={setColor}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <div
                              className={`w-4 h-4 rounded-full ${option.class}`}
                            ></div>
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priorité</Label>
                  <Select value={priority} onValueChange={setPriority}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Responsables assignés</Label>
                  <div className="border rounded-md p-3 max-h-32 overflow-y-auto">
                    {users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center space-x-2 py-1"
                      >
                        <Checkbox
                          id={user.id}
                          checked={selectedUserIds.includes(user.id)}
                          onCheckedChange={(checked) =>
                            handleUserSelection(user.id, checked as boolean)
                          }
                        />
                        <Label htmlFor={user.id} className="text-sm">
                          {user.name} ({user.email})
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingTask ? "Mettre à jour" : "Créer"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-green-600">
              {message}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Tasks Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tâches
              </CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tasks.length}</div>
              <p className="text-xs text-muted-foreground">Toutes les tâches</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminées</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.filter((task) => task.status === "FAIT").length}
              </div>
              <p className="text-xs text-muted-foreground">Complétées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Urgentes</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.filter((task) => task.priority === "URGENT").length}
              </div>
              <p className="text-xs text-muted-foreground">À prioriser</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">KPIs Totaux</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.reduce((total, task) => total + task.kpis.length, 0)}
              </div>
              <p className="text-xs text-muted-foreground">Indicateurs créés</p>
            </CardContent>
          </Card>
        </div>

        {/* Tasks Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Tâches</CardTitle>
            <CardDescription>
              Toutes les tâches avec leurs détails et KPIs associés
            </CardDescription>
          </CardHeader>
          <CardContent>
            {tasks.length === 0 ? (
              <div className="text-center py-8">
                <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Aucune tâche trouvée</p>
              </div>
            ) : (
              <div className="space-y-4">
                {tasks.map((task) => (
                  <Card
                    key={task.id}
                    className="hover:shadow-md transition-shadow"
                  >
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: task.color }}
                          ></div>
                          <CardTitle className="text-lg">
                            {task.title}
                          </CardTitle>
                          {getStatusBadge(task.status)}
                          {getPriorityBadge(task.priority)}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDetailsDialog(task)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(task)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(task.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {task.description && (
                        <p className="text-muted-foreground mb-4">
                          {task.description}
                        </p>
                      )}

                      <div className="grid gap-4 md:grid-cols-2 mb-4">
                        <div>
                          <Label className="text-sm font-medium">
                            Informations
                          </Label>
                          <div className="text-sm text-muted-foreground space-y-1">
                            <p>Segment: {task.segment.title}</p>
                            <p>Cellule: {task.segment.cellule.title}</p>
                            <p>Créé par: {task.createdBy.name}</p>
                            <p>
                              Date:{" "}
                              {new Date(task.createdAt).toLocaleDateString(
                                "fr-FR"
                              )}
                            </p>
                          </div>
                        </div>

                        <div>
                          <Label className="text-sm font-medium">
                            Responsables assignés
                          </Label>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {task.assignees.map((assignee) => (
                              <Badge
                                key={assignee.user.id}
                                variant="outline"
                                className="text-xs"
                              >
                                {assignee.user.name}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* KPIs Display */}
                      {task.kpis.length > 0 && (
                        <div>
                          <Label className="text-sm font-medium mb-2 block">
                            KPIs associés ({task.kpis.length}):
                          </Label>
                          <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                            {task.kpis.map((kpi) => (
                              <div
                                key={kpi.id}
                                className="p-3 border rounded-lg bg-accent/50"
                              >
                                <div className="flex items-center justify-between mb-1">
                                  <h4 className="font-medium text-sm">
                                    {kpi.title}
                                  </h4>
                                  <Badge
                                    variant="outline"
                                    className="text-lg font-bold"
                                  >
                                    {kpi.value}
                                  </Badge>
                                </div>
                                {kpi.description && (
                                  <p className="text-xs text-muted-foreground mb-1">
                                    {kpi.description}
                                  </p>
                                )}
                                <p className="text-xs text-muted-foreground">
                                  Par: {kpi.createdBy.name}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Task Details Modal */}
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>
                Détails de la Tâche: {selectedTask?.title}
              </DialogTitle>
              <DialogDescription>
                Informations complètes de la tâche et ses KPIs
              </DialogDescription>
            </DialogHeader>
            {selectedTask && (
              <div className="space-y-6">
                {/* Task Info */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium">Statut</Label>
                    <div className="mt-1">
                      {getStatusBadge(selectedTask.status)}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Priorité</Label>
                    <div className="mt-1">
                      {getPriorityBadge(selectedTask.priority)}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Segment</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedTask.segment.title}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Cellule</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedTask.segment.cellule.title}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Créé par</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedTask.createdBy.name}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">
                      Date de création
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {new Date(selectedTask.createdAt).toLocaleDateString(
                        "fr-FR"
                      )}
                    </p>
                  </div>
                </div>

                {selectedTask.description && (
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedTask.description}
                    </p>
                  </div>
                )}

                {/* Assignees */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">
                    Responsables assignés
                  </Label>
                  {selectedTask.assignees.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {selectedTask.assignees.map((assignee) => (
                        <Badge key={assignee.user.id} variant="outline">
                          {assignee.user.name} ({assignee.user.email})
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      Aucun responsable assigné
                    </p>
                  )}
                </div>

                {/* KPIs */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">
                    KPIs associés ({selectedTask.kpis.length})
                  </Label>
                  {selectedTask.kpis.length > 0 ? (
                    <div className="grid gap-3 md:grid-cols-2">
                      {selectedTask.kpis.map((kpi) => (
                        <div
                          key={kpi.id}
                          className="p-4 border rounded-lg bg-accent/50"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">{kpi.title}</h4>
                            <Badge
                              variant="outline"
                              className="text-lg font-bold"
                            >
                              {kpi.value}
                            </Badge>
                          </div>
                          {kpi.description && (
                            <p className="text-xs text-muted-foreground mb-2">
                              {kpi.description}
                            </p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            Créé par: {kpi.createdBy.name}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      Aucun KPI associé à cette tâche
                    </p>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
