'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { BarChart3, Plus, Edit, Trash2, TrendingUp, Eye } from 'lucide-react'

interface Task {
  id: string
  title: string
  segment: {
    title: string
  }
}

interface Kpi {
  id: string
  title: string
  description: string
  value: number
  createdAt: string
  task: {
    id: string
    title: string
    segment: {
      title: string
    }
  }
}

export default function ResponsableKpisPage() {
  const { data: session } = useSession()
  const [kpis, setKpis] = useState<Kpi[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [editingKpi, setEditingKpi] = useState<Kpi | null>(null)
  const [selectedKpi, setSelectedKpi] = useState<Kpi | null>(null)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  // Form state
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [value, setValue] = useState('')
  const [selectedTaskId, setSelectedTaskId] = useState('')

  useEffect(() => {
    fetchKpis()
    fetchTasks()
  }, [])

  const fetchKpis = async () => {
    try {
      const response = await fetch('/api/responsable/kpis')
      if (response.ok) {
        const data = await response.json()
        setKpis(data.kpis)
      }
    } catch (error) {
      setError('Erreur lors du chargement des KPIs')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchTasks = async () => {
    try {
      const response = await fetch('/api/responsable/tasks')
      if (response.ok) {
        const data = await response.json()
        setTasks(data.tasks)
      }
    } catch (error) {
      console.error('Erreur lors du chargement des tâches')
    }
  }

  const resetForm = () => {
    setTitle('')
    setDescription('')
    setValue('')
    setSelectedTaskId('')
    setEditingKpi(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')

    const kpiData = {
      title,
      description,
      value: parseFloat(value),
      taskId: selectedTaskId,
    }

    try {
      const url = editingKpi 
        ? `/api/responsable/kpis/${editingKpi.id}`
        : '/api/responsable/kpis'
      
      const method = editingKpi ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(kpiData),
      })

      if (response.ok) {
        setMessage(editingKpi ? 'KPI mis à jour avec succès' : 'KPI créé avec succès')
        setIsDialogOpen(false)
        resetForm()
        fetchKpis()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la sauvegarde')
      }
    } catch (error) {
      setError('Une erreur est survenue')
    }
  }

  const handleEdit = (kpi: Kpi) => {
    setEditingKpi(kpi)
    setTitle(kpi.title)
    setDescription(kpi.description)
    setValue(kpi.value.toString())
    setSelectedTaskId(kpi.task.id)
    setIsDialogOpen(true)
  }

  const handleDelete = async (kpiId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce KPI ?')) {
      return
    }

    try {
      const response = await fetch(`/api/responsable/kpis/${kpiId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setMessage('KPI supprimé avec succès')
        fetchKpis()
      } else {
        setError('Erreur lors de la suppression')
      }
    } catch (error) {
      setError('Une erreur est survenue')
    }
  }

  const openCreateDialog = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  const openDetailsDialog = (kpi: Kpi) => {
    setSelectedKpi(kpi)
    setIsDetailsOpen(true)
  }

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={['RESPONSABLE_TACHE']}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout requiredRole={['RESPONSABLE_TACHE']}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Mes KPIs</h1>
            <p className="text-muted-foreground">
              Gérez les indicateurs de performance de vos tâches
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Nouveau KPI
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingKpi ? 'Modifier le KPI' : 'Créer un nouveau KPI'}
                </DialogTitle>
                <DialogDescription>
                  {editingKpi 
                    ? 'Modifiez les informations de ce KPI'
                    : 'Ajoutez un nouvel indicateur de performance pour une de vos tâches'
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre du KPI</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Ex: Taux de completion"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Description détaillée du KPI"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="value">Valeur</Label>
                  <Input
                    id="value"
                    type="number"
                    step="0.01"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    placeholder="Ex: 85.5"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="task">Tâche associée</Label>
                  <Select value={selectedTaskId} onValueChange={setSelectedTaskId} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une tâche" />
                    </SelectTrigger>
                    <SelectContent>
                      {tasks.map((task) => (
                        <SelectItem key={task.id} value={task.id}>
                          {task.title} - {task.segment.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingKpi ? 'Mettre à jour' : 'Créer'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-green-600">{message}</AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* KPIs Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total KPIs</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.length}</div>
              <p className="text-xs text-muted-foreground">
                Indicateurs créés
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Valeur Moyenne</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpis.length > 0 
                  ? (kpis.reduce((sum, kpi) => sum + kpi.value, 0) / kpis.length).toFixed(1)
                  : '0'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                Performance globale
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Valeur Max</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpis.length > 0 
                  ? Math.max(...kpis.map(kpi => kpi.value)).toFixed(1)
                  : '0'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                Meilleure performance
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tâches Suivies</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(kpis.map(kpi => kpi.task.id)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Avec indicateurs
              </p>
            </CardContent>
          </Card>
        </div>

        {/* KPIs List */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des KPIs</CardTitle>
            <CardDescription>
              Tous vos indicateurs de performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            {kpis.length === 0 ? (
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Aucun KPI créé pour le moment</p>
                <p className="text-sm text-muted-foreground">
                  Commencez par créer votre premier indicateur de performance
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {kpis.map((kpi) => (
                  <div
                    key={kpi.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium">{kpi.title}</h3>
                        <Badge variant="secondary" className="text-lg font-bold">
                          {kpi.value}
                        </Badge>
                      </div>
                      {kpi.description && (
                        <p className="text-sm text-muted-foreground mb-2">
                          {kpi.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <span>Tâche: {kpi.task.title}</span>
                        <span>Segment: {kpi.task.segment.title}</span>
                        <span>Créé le {new Date(kpi.createdAt).toLocaleDateString('fr-FR')}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openDetailsDialog(kpi)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(kpi)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(kpi.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* KPI Details Modal */}
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Détails du KPI: {selectedKpi?.title}</DialogTitle>
              <DialogDescription>
                Informations complètes de l'indicateur de performance
              </DialogDescription>
            </DialogHeader>
            {selectedKpi && (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary mb-2">
                    {selectedKpi.value}
                  </div>
                  <h3 className="text-lg font-medium">{selectedKpi.title}</h3>
                </div>

                {selectedKpi.description && (
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground mt-1">{selectedKpi.description}</p>
                  </div>
                )}

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium">Tâche associée</Label>
                    <p className="text-sm text-muted-foreground">{selectedKpi.task.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Segment</Label>
                    <p className="text-sm text-muted-foreground">{selectedKpi.task.segment.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Date de création</Label>
                    <p className="text-sm text-muted-foreground">
                      {new Date(selectedKpi.createdAt).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Dernière mise à jour</Label>
                    <p className="text-sm text-muted-foreground">
                      {new Date(selectedKpi.createdAt).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}