import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    // Get tasks assigned to this responsable
    const tasks = await prisma.task.findMany({
      where: {
        assignees: {
          some: {
            userId: session.user.id,
          }
        }
      },
      include: {
        segment: {
          select: {
            title: true,
          }
        },
        kpis: true,
      }
    })

    const totalTasks = tasks.length
    const completedTasks = tasks.filter(task => task.status === 'FAIT').length
    const inProgressTasks = tasks.filter(task => task.status === 'NON_FAIT').length
    const urgentTasks = tasks.filter(task => task.priority === 'URGENT').length
    const totalKpis = tasks.reduce((sum, task) => sum + task.kpis.length, 0)

    // Recent tasks (prioritize urgent and incomplete)
    const recentTasks = tasks
      .sort((a, b) => {
        // Sort by priority first, then by status
        if (a.priority === 'URGENT' && b.priority !== 'URGENT') return -1
        if (b.priority === 'URGENT' && a.priority !== 'URGENT') return 1
        if (a.status === 'NON_FAIT' && b.status === 'FAIT') return -1
        if (b.status === 'NON_FAIT' && a.status === 'FAIT') return 1
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      })
      .slice(0, 5)
      .map(task => ({
        id: task.id,
        title: task.title,
        status: task.status,
        priority: task.priority,
        segment: {
          title: task.segment.title
        }
      }))

    // Progress by segment
    const segmentProgress = tasks.reduce((acc, task) => {
      const segmentName = task.segment.title
      if (!acc[segmentName]) {
        acc[segmentName] = { total: 0, completed: 0 }
      }
      acc[segmentName].total++
      if (task.status === 'FAIT') {
        acc[segmentName].completed++
      }
      return acc
    }, {} as Record<string, { total: number, completed: number }>)

    const progressBySegment = Object.entries(segmentProgress).map(([segment, data]) => ({
      segment,
      completed: data.completed,
      total: data.total,
      percentage: data.total > 0 ? Math.round((data.completed / data.total) * 100) : 0
    }))

    return NextResponse.json({
      totalTasks,
      completedTasks,
      inProgressTasks,
      urgentTasks,
      totalKpis,
      recentTasks,
      progressBySegment
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des statistiques' },
      { status: 500 }
    )
  }
}