import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    // Verify that the segment belongs to the current user
    const segment = await prisma.segment.findFirst({
      where: {
        id: params.id,
        createdById: session.user.id,
      },
      include: {
        tasks: {
          include: {
            assignees: {
              include: {
                user: {
                  select: {
                    name: true,
                  }
                }
              }
            },
            kpis: {
              select: {
                id: true,
                title: true,
                value: true,
              }
            }
          }
        }
      }
    })

    if (!segment) {
      return NextResponse.json(
        { error: 'Segment non trouvé' },
        { status: 404 }
      )
    }

    return NextResponse.json({ tasks: segment.tasks })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des détails' },
      { status: 500 }
    )
  }
}