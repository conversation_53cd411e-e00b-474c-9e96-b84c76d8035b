"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckSquare, Clock, AlertCircle, Check } from "lucide-react";

interface ResponsableStats {
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  urgentTasks: number;
  totalKpis: number;
  recentTasks: {
    id: string;
    title: string;
    status: string;
    priority: string;
    segment: {
      title: string;
    };
  }[];
  progressBySegment: {
    segment: string;
    completed: number;
    total: number;
    percentage: number;
  }[];
}
export default function ResponsableDashboard() {
  const [stats, setStats] = useState<ResponsableStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchResponsableStats();
  }, []);

  const fetchResponsableStats = async () => {
    try {
      const response = await fetch("/api/responsable/dashboard-stats");
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["RESPONSABLE_TACHE"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["RESPONSABLE_TACHE"]}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Tableau de bord Responsable
          </h1>
          <p className="text-muted-foreground">
            Gérez vos tâches assignées et suivez votre progression
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Mes Tâches</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalTasks || 0}</div>
              <p className="text-xs text-muted-foreground">Tâches assignées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Cours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.inProgressTasks || 0}
              </div>
              <p className="text-xs text-muted-foreground">À traiter</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminées</CardTitle>
              <Check className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.completedTasks || 0}
              </div>
              <p className="text-xs text-muted-foreground">Complétées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Urgentes</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.urgentTasks || 0}
              </div>
              <p className="text-xs text-muted-foreground">À prioriser</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Tâches Prioritaires</CardTitle>
              <CardDescription>Vos tâches les plus importantes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.recentTasks && stats.recentTasks.length > 0 ? (
                  stats.recentTasks.map((task) => (
                    <div
                      key={task.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium">{task.title}</p>
                        <p className="text-sm text-muted-foreground">
                          Segment: {task.segment.title}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          className={
                            task.priority === "URGENT"
                              ? "bg-red-100 text-red-800"
                              : task.priority === "NORMAL"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                          }
                        >
                          {task.priority === "URGENT"
                            ? "Urgent"
                            : task.priority === "NORMAL"
                            ? "Normal"
                            : "Faible"}
                        </Badge>
                        <Badge
                          className={
                            task.status === "FAIT"
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                          }
                        >
                          {task.status === "FAIT" ? "Terminée" : "En cours"}
                        </Badge>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Aucune tâche assignée pour le moment
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Progression Globale</CardTitle>
              <CardDescription>
                Votre avancement sur tous les projets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.progressBySegment &&
                stats.progressBySegment.length > 0 ? (
                  stats.progressBySegment.map((progress, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between text-sm">
                        <span>{progress.segment}</span>
                        <span>
                          {progress.completed}/{progress.total} tâches
                        </span>
                      </div>
                      <div className="w-full bg-secondary rounded-full h-2 mt-1">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Aucune progression à afficher
                  </p>
                )}

                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between text-sm font-medium">
                    <span>Total</span>
                    <span className="text-green-600">
                      {stats?.completedTasks || 0}/{stats?.totalTasks || 0}{" "}
                      terminées
                    </span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-3 mt-2">
                    <div
                      className="bg-green-600 h-3 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          stats?.totalTasks
                            ? (stats.completedTasks / stats.totalTasks) * 100
                            : 0
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
