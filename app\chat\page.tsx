"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  MessageCircle,
  Send,
  Plus,
  Users,
  Search,
  MoreVertical,
  Info,
} from "lucide-react";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
  role: string;
}

interface Message {
  id: string;
  content: string;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    email: string;
    image?: string;
    role: string;
  };
  receiver: {
    id: string;
    name: string;
    email: string;
    image?: string;
    role: string;
  };
}

interface Conversation {
  user: User;
  lastMessage?: Message;
  unreadCount: number;
}

const roleColors = {
  ADMIN: "bg-red-100 text-red-800",
  PILOTE_CELLULE: "bg-blue-100 text-blue-800",
  RESPONSABLE_TACHE: "bg-green-100 text-green-800",
  UTILISATEUR: "bg-gray-100 text-gray-800",
};

const roleLabels = {
  ADMIN: "Admin",
  PILOTE_CELLULE: "Pilote",
  RESPONSABLE_TACHE: "Responsable",
  UTILISATEUR: "Utilisateur",
};

export default function ChatPage() {
  const { data: session } = useSession();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isNewChatOpen, setIsNewChatOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchConversations();
    fetchUsers();
  }, []);

  useEffect(() => {
    if (selectedUser) {
      fetchMessages(selectedUser.id);
    }
  }, [selectedUser]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Poll for new messages every 3 seconds
    const interval = setInterval(() => {
      if (selectedUser) {
        fetchMessages(selectedUser.id);
      }
      fetchConversations();
    }, 3000);

    return () => clearInterval(interval);
  }, [selectedUser]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const fetchConversations = async () => {
    try {
      const response = await fetch("/api/chat/conversations");
      if (response.ok) {
        const data = await response.json();
        setConversations(data.conversations);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des conversations");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/chat/users");
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs");
    }
  };

  const fetchMessages = async (userId: string) => {
    try {
      const response = await fetch(`/api/chat/messages?userId=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des messages");
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedUser) return;

    setError("");

    try {
      const response = await fetch("/api/chat/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: newMessage,
          receiverId: selectedUser.id,
        }),
      });

      if (response.ok) {
        setNewMessage("");
        fetchMessages(selectedUser.id);
        fetchConversations();
      } else {
        setError("Erreur lors de l'envoi du message");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const startNewChat = (user: User) => {
    setSelectedUser(user);
    setIsNewChatOpen(false);
    setMessages([]);
  };

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getRoleBadge = (role: string) => {
    return (
      <Badge
        className={roleColors[role as keyof typeof roleColors]}
        variant="secondary"
      >
        {roleLabels[role as keyof typeof roleLabels]}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="h-[calc(100vh-8rem)] flex bg-white dark:bg-gray-900 rounded-xl overflow-hidden shadow-lg border">
        {/* Sidebar des conversations */}
        <div className="w-80 border-r bg-card">
          <div className="p-4 border-b bg-card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold flex items-center">
                <MessageCircle className="h-5 w-5 mr-2" />
                Messages
              </h2>
              <Dialog open={isNewChatOpen} onOpenChange={setIsNewChatOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" variant="outline">
                    <Plus className="h-4 w-4 mr-1" />
                    Nouveau
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Nouvelle conversation</DialogTitle>
                    <DialogDescription>
                      Sélectionnez un utilisateur pour commencer une
                      conversation
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Rechercher un utilisateur..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <ScrollArea className="h-60">
                      <div className="space-y-2">
                        {filteredUsers.map((user) => (
                          <div
                            key={user.id}
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted cursor-pointer transition-colors"
                            onClick={() => startNewChat(user)}
                          >
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.image || ""} />
                              <AvatarFallback>
                                {user.name.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">
                                {user.name}
                              </p>
                              <p className="text-xs text-muted-foreground truncate">
                                {user.email}
                              </p>
                            </div>
                            {getRoleBadge(user.role)}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-2">
              {conversations.length === 0 ? (
                <div className="text-center py-8">
                  <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Aucune conversation</p>
                  <p className="text-sm text-muted-foreground">
                    Commencez une nouvelle conversation
                  </p>
                </div>
              ) : (
                conversations.map((conversation) => (
                  <div
                    key={conversation.user.id}
                    className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer hover:bg-muted transition-all duration-200 ${
                      selectedUser?.id === conversation.user.id
                        ? "bg-muted border-l-4 border-primary"
                        : ""
                    }`}
                    onClick={() => setSelectedUser(conversation.user)}
                  >
                    <div className="relative">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={conversation.user.image || ""} />
                        <AvatarFallback>
                          {conversation.user.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      {conversation.unreadCount > 0 && (
                        <div className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                          {conversation.unreadCount}
                        </div>
                      )}
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-900"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium truncate">
                          {conversation.user.name}
                        </p>
                        <div className="flex items-center space-x-1">
                          {getRoleBadge(conversation.user.role)}
                          {conversation.lastMessage && (
                            <span className="text-xs text-muted-foreground">
                              {new Date(
                                conversation.lastMessage.createdAt
                              ).toLocaleTimeString("fr-FR", {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </span>
                          )}
                        </div>
                      </div>
                      {conversation.lastMessage && (
                        <p className="text-xs text-muted-foreground truncate mt-1">
                          {conversation.lastMessage.content}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Zone de chat */}
        <div className="flex-1 flex flex-col">
          {selectedUser ? (
            <>
              {/* Header du chat */}
              <div className="p-4 border-b bg-card">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={selectedUser.image || ""} />
                      <AvatarFallback>
                        {selectedUser.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background"></div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">
                      {selectedUser.name}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <p className="text-sm text-muted-foreground">
                        {selectedUser.email}
                      </p>
                      <span className="text-muted-foreground">•</span>
                      <span className="text-xs text-muted-foreground">
                        En ligne
                      </span>
                      <span className="text-muted-foreground">•</span>
                      {getRoleBadge(selectedUser.role)}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Info className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <ScrollArea className="flex-1 p-4 bg-background">
                <div className="space-y-6">
                  {messages.length === 0 ? (
                    <div className="text-center py-8">
                      <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Aucun message</p>
                      <p className="text-sm text-muted-foreground">
                        Commencez la conversation
                      </p>
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex items-end space-x-2 ${
                          message.sender.id === session?.user?.id
                            ? "justify-end"
                            : "justify-start"
                        }`}
                      >
                        {message.sender.id !== session?.user?.id && (
                          <Avatar className="h-8 w-8 mb-1">
                            <AvatarImage src={message.sender.image || ""} />
                            <AvatarFallback className="text-xs">
                              {message.sender.name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div className="flex flex-col">
                          <div
                            className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                              message.sender.id === session?.user?.id
                                ? "bg-primary text-primary-foreground rounded-br-md"
                                : "bg-card border rounded-bl-md"
                            }`}
                          >
                            <p className="text-sm leading-relaxed">
                              {message.content}
                            </p>
                          </div>
                          <p
                            className={`text-xs mt-1 px-2 ${
                              message.sender.id === session?.user?.id
                                ? "text-right text-muted-foreground"
                                : "text-muted-foreground"
                            }`}
                          >
                            {new Date(message.createdAt).toLocaleTimeString(
                              "fr-FR",
                              {
                                hour: "2-digit",
                                minute: "2-digit",
                              }
                            )}
                          </p>
                        </div>
                        {message.sender.id === session?.user?.id && (
                          <Avatar className="h-8 w-8 mb-1">
                            <AvatarImage src={session.user.image || ""} />
                            <AvatarFallback className="text-xs">
                              {session.user.name?.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Zone d'envoi */}
              <div className="p-4 border-t bg-card">
                {error && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <form onSubmit={sendMessage} className="flex space-x-3">
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Tapez votre message..."
                    className="flex-1 rounded-full"
                  />
                  <Button
                    type="submit"
                    disabled={!newMessage.trim()}
                    className="rounded-full px-6"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </form>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  Sélectionnez une conversation
                </h3>
                <p className="text-muted-foreground">
                  Choisissez une conversation existante ou commencez-en une
                  nouvelle
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
