import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    // Get total counts
    const [totalUsers, totalCellules, totalTasks, totalKpis] = await Promise.all([
      prisma.user.count(),
      prisma.cellule.count(),
      prisma.task.count(),
      prisma.kpi.count(),
    ])

    // Get recent activity (last 10 activities)
    const recentTasks = await prisma.task.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        createdBy: { select: { name: true } }
      }
    })

    const recentUsers = await prisma.user.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      select: { name: true, createdAt: true }
    })

    const recentActivity = [
      ...recentTasks.map(task => ({
        type: 'task',
        message: `Nouvelle tâche "${task.title}" créée par ${task.createdBy.name}`,
        time: new Date(task.createdAt).toLocaleDateString('fr-FR')
      })),
      ...recentUsers.map(user => ({
        type: 'user',
        message: `Nouvel utilisateur ${user.name} inscrit`,
        time: new Date(user.createdAt).toLocaleDateString('fr-FR')
      }))
    ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 5)

    // Get project progress (cellules with task completion rates)
    const cellules = await prisma.cellule.findMany({
      include: {
        segments: {
          include: {
            tasks: {
              select: {
                status: true
              }
            }
          }
        }
      }
    })

    const projectProgress = cellules.map(cellule => {
      const allTasks = cellule.segments.flatMap(segment => segment.tasks)
      const completedTasks = allTasks.filter(task => task.status === 'FAIT').length
      const progress = allTasks.length > 0 ? Math.round((completedTasks / allTasks.length) * 100) : 0
      
      return {
        name: cellule.title,
        progress,
        color: cellule.color
      }
    })

    return NextResponse.json({
      totalUsers,
      totalCellules,
      totalTasks,
      totalKpis,
      recentActivity,
      projectProgress
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des statistiques' },
      { status: 500 }
    )
  }
}