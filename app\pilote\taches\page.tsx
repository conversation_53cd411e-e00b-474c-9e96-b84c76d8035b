"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckSquare, Plus, Edit, Trash2, <PERSON>, <PERSON> } from "lucide-react";

interface Segment {
  id: string;
  title: string;
  cellule: {
    title: string;
  };
}

interface User {
  id: string;
  name: string;
  email: string;
}

interface Task {
  id: string;
  title: string;
  description: string;
  color: string;
  priority: "URGENT" | "NORMAL" | "NOT_IMPORTANT";
  status: "FAIT" | "NON_FAIT";
  createdAt: string;
  segment: {
    id: string;
    title: string;
    cellule: {
      title: string;
    };
  };
  assignees: {
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
  _count: {
    kpis: number;
  };
}

const colorOptions = [
  { value: "#3B82F6", label: "Bleu", class: "bg-blue-500" },
  { value: "#8B5CF6", label: "Violet", class: "bg-violet-500" },
  { value: "#06B6D4", label: "Cyan", class: "bg-cyan-500" },
  { value: "#10B981", label: "Vert", class: "bg-emerald-500" },
  { value: "#F59E0B", label: "Orange", class: "bg-amber-500" },
  { value: "#EF4444", label: "Rouge", class: "bg-red-500" },
];

const priorityOptions = [
  { value: "NOT_IMPORTANT", label: "Faible" },
  { value: "NORMAL", label: "Normale" },
  { value: "URGENT", label: "Urgent" },
];

export default function PiloteTachesPage() {
  const { data: session } = useSession();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [segments, setSegments] = useState<Segment[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState("#3B82F6");
  const [selectedSegmentId, setSelectedSegmentId] = useState("");
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [priority, setPriority] = useState("NORMAL");

  useEffect(() => {
    fetchTasks();
    fetchSegments();
    fetchUsers();
  }, []);

  const fetchTasks = async () => {
    try {
      const response = await fetch("/api/pilote/tasks");
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks);
      }
    } catch (error) {
      setError("Erreur lors du chargement des tâches");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSegments = async () => {
    try {
      const response = await fetch("/api/pilote/segments");
      if (response.ok) {
        const data = await response.json();
        setSegments(data.segments);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des segments");
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/pilote/users");
      if (response.ok) {
        const data = await response.json();
        console.log("Pilote - Responsable users found:", data.users);
        setUsers(data.users);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs:", error);
      setError("Erreur lors du chargement des utilisateurs");
    }
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setColor("#3B82F6");
    setSelectedSegmentId("");
    setSelectedUserIds([]);
    setPriority("NORMAL");
    setEditingTask(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");

    // Validation côté client
    if (!title.trim()) {
      setError("Le titre est requis");
      return;
    }

    if (!selectedSegmentId) {
      setError("Veuillez sélectionner un segment");
      return;
    }

    const taskData = {
      title: title.trim(),
      description: description.trim(),
      color,
      priority,
      segmentId: selectedSegmentId,
      assigneeIds: selectedUserIds,
    };

    console.log("Pilote sending task data:", taskData);

    try {
      const url = editingTask
        ? `/api/pilote/tasks/${editingTask.id}`
        : "/api/pilote/tasks";

      const method = editingTask ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(taskData),
      });

      if (response.ok) {
        setMessage(
          editingTask
            ? "Tâche mise à jour avec succès"
            : "Tâche créée avec succès"
        );
        setIsDialogOpen(false);
        resetForm();
        fetchTasks();
      } else {
        const data = await response.json();
        console.error("Pilote API Error:", data);
        setError(
          data.details
            ? `${data.error}: ${data.details}`
            : data.error || "Erreur lors de la sauvegarde"
        );
      }
    } catch (error) {
      console.error("Pilote Request Error:", error);
      setError(
        "Une erreur est survenue: " +
          (error instanceof Error ? error.message : String(error))
      );
    }
  };

  const handleEdit = (task: Task) => {
    setEditingTask(task);
    setTitle(task.title);
    setDescription(task.description);
    setColor(task.color);
    setPriority(task.priority);
    setSelectedSegmentId(task.segment.id);
    setSelectedUserIds(task.assignees.map((assignee) => assignee.user.id));
    setIsDialogOpen(true);
  };

  const handleDelete = async (taskId: string) => {
    if (
      !confirm(
        "Êtes-vous sûr de vouloir supprimer cette tâche ? Tous les KPIs associés seront également supprimés."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/pilote/tasks/${taskId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setMessage("Tâche supprimée avec succès");
        fetchTasks();
      } else {
        setError("Erreur lors de la suppression");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const handleUserSelection = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUserIds([...selectedUserIds, userId]);
    } else {
      setSelectedUserIds(selectedUserIds.filter((id) => id !== userId));
    }
  };

  const openCreateDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    return status === "FAIT" ? (
      <Badge className="bg-green-100 text-green-800">Terminée</Badge>
    ) : (
      <Badge variant="secondary">En cours</Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["PILOTE_CELLULE"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["PILOTE_CELLULE"]}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Mes Tâches</h1>
            <p className="text-muted-foreground">
              Gérez les tâches de vos segments et assignez-les aux responsables
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Tâche
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingTask
                    ? "Modifier la tâche"
                    : "Créer une nouvelle tâche"}
                </DialogTitle>
                <DialogDescription>
                  {editingTask
                    ? "Modifiez les informations de cette tâche"
                    : "Ajoutez une nouvelle tâche et assignez-la aux responsables"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre de la tâche</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Ex: Développer l'interface utilisateur"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Description détaillée de la tâche"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="segment">Segment</Label>
                  <Select
                    value={selectedSegmentId}
                    onValueChange={setSelectedSegmentId}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un segment" />
                    </SelectTrigger>
                    <SelectContent>
                      {segments.map((segment) => (
                        <SelectItem key={segment.id} value={segment.id}>
                          {segment.title} - {segment.cellule.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Couleur</Label>
                  <Select value={color} onValueChange={setColor}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <div
                              className={`w-4 h-4 rounded-full ${option.class}`}
                            ></div>
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priorité</Label>
                  <Select value={priority} onValueChange={setPriority}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Responsables assignés</Label>
                  <div className="border rounded-md p-3 max-h-32 overflow-y-auto">
                    {users
                      .filter((user) => user.id !== session?.user?.id)
                      .map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center space-x-2 py-1"
                        >
                          <Checkbox
                            id={user.id}
                            checked={selectedUserIds.includes(user.id)}
                            onCheckedChange={(checked) =>
                              handleUserSelection(user.id, checked as boolean)
                            }
                          />
                          <Label htmlFor={user.id} className="text-sm">
                            {user.name} ({user.email})
                          </Label>
                        </div>
                      ))}
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingTask ? "Mettre à jour" : "Créer"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-green-600">
              {message}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Tasks Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tâches
              </CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tasks.length}</div>
              <p className="text-xs text-muted-foreground">Tâches créées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminées</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.filter((task) => task.status === "FAIT").length}
              </div>
              <p className="text-xs text-muted-foreground">Tâches complétées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Cours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.filter((task) => task.status === "NON_FAIT").length}
              </div>
              <p className="text-xs text-muted-foreground">À terminer</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">KPIs Totaux</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.reduce((total, task) => total + task._count.kpis, 0)}
              </div>
              <p className="text-xs text-muted-foreground">Indicateurs créés</p>
            </CardContent>
          </Card>
        </div>

        {/* Tasks List */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Tâches</CardTitle>
            <CardDescription>
              Toutes vos tâches organisées par segment
            </CardDescription>
          </CardHeader>
          <CardContent>
            {tasks.length === 0 ? (
              <div className="text-center py-8">
                <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Aucune tâche créée pour le moment
                </p>
                <p className="text-sm text-muted-foreground">
                  Commencez par créer votre première tâche
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: task.color }}
                        ></div>
                        <h3 className="font-medium">{task.title}</h3>
                        {getStatusBadge(task.status)}
                      </div>
                      {task.description && (
                        <p className="text-sm text-muted-foreground mb-2">
                          {task.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground mb-2">
                        <span>Segment: {task.segment.title}</span>
                        <span>Cellule: {task.segment.cellule.title}</span>
                        <span>{task._count.kpis} KPIs</span>
                        <span>
                          Créée le{" "}
                          {new Date(task.createdAt).toLocaleDateString("fr-FR")}
                        </span>
                      </div>
                      {task.assignees.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <div className="flex flex-wrap gap-1">
                            {task.assignees.map((assignee) => (
                              <Badge
                                key={assignee.user.id}
                                variant="outline"
                                className="text-xs"
                              >
                                {assignee.user.name}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(task)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(task.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
