import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const kpis = await prisma.kpi.findMany({
      include: {
        task: {
          include: {
            segment: {
              include: {
                cellule: {
                  select: {
                    title: true,
                  }
                }
              }
            }
          }
        },
        createdBy: {
          select: {
            name: true,
            email: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json({ kpis })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des KPIs' },
      { status: 500 }
    )
  }
}