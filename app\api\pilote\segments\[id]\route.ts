import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateSegmentSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  celluleId: z.string(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, color, celluleId } = updateSegmentSchema.parse(body)

    // Verify that the segment belongs to the current user
    const existingSegment = await prisma.segment.findFirst({
      where: {
        id: params.id,
        createdById: session.user.id,
      }
    })

    if (!existingSegment) {
      return NextResponse.json(
        { error: 'Segment non trouvé' },
        { status: 404 }
      )
    }

    // Verify that the cellule exists and the user has access to it
    const cellule = await prisma.cellule.findFirst({
      where: {
        id: celluleId,
        users: {
          some: {
            userId: session.user.id,
          }
        }
      }
    })

    if (!cellule) {
      return NextResponse.json(
        { error: 'Cellule non trouvée ou accès non autorisé' },
        { status: 404 }
      )
    }

    const segment = await prisma.segment.update({
      where: { id: params.id },
      data: {
        title,
        description,
        color,
        celluleId,
      },
      include: {
        cellule: {
          select: {
            id: true,
            title: true,
          }
        },
        _count: {
          select: {
            tasks: true,
          }
        }
      }
    })

    return NextResponse.json({ segment })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la mise à jour du segment' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    // Verify that the segment belongs to the current user
    const existingSegment = await prisma.segment.findFirst({
      where: {
        id: params.id,
        createdById: session.user.id,
      }
    })

    if (!existingSegment) {
      return NextResponse.json(
        { error: 'Segment non trouvé' },
        { status: 404 }
      )
    }

    await prisma.segment.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Segment supprimé avec succès' })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la suppression du segment' },
      { status: 500 }
    )
  }
}