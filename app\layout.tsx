import "./globals.css";
import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import Providers from "@/components/providers/session-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";

const gantari = Gantari({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-gantari",
});

export const metadata: Metadata = {
  title: "Colloque ENCG",
  description: "Gestion des tâches",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="fr">
      <body className={`${gantari.variable} font-sans`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>{children}</Providers>
        </ThemeProvider>
      </body>
    </html>
  );
}
