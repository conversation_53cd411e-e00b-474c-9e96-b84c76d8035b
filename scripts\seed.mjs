import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  // Hash password for test accounts
  const hashedPassword = await bcrypt.hash("password123", 12);

  // Create test users
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Administrateur ENCG",
      hashedPassword,
      role: "ADMIN",
    },
  });

  const pilote = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Pilote Cellule",
      hashedPassword,
      role: "PILOTE_CELLULE",
    },
  });

  const responsable = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Responsable Tâche",
      hashedPassword,
      role: "RESPONSABLE_TACHE",
    },
  });

  const utilisateur = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Utilisateur Standard",
      hashedPassword,
      role: "UTILISATEUR",
    },
  });

  // Create sample cellules
  const celluleWeb = await prisma.cellule.create({
    data: {
      title: "Développement Web",
      description: "Projets de développement web et applications",
      color: "#3B82F6",
    },
  });

  const celluleMarketing = await prisma.cellule.create({
    data: {
      title: "Marketing Digital",
      description: "Stratégies et campagnes marketing digitales",
      color: "#8B5CF6",
    },
  });

  // Create segments
  const segmentFrontend = await prisma.segment.create({
    data: {
      title: "Frontend Development",
      description: "Interface utilisateur et expérience",
      color: "#06B6D4",
      celluleId: celluleWeb.id,
      createdById: pilote.id,
    },
  });

  const segmentBackend = await prisma.segment.create({
    data: {
      title: "Backend Development",
      description: "API et logique serveur",
      color: "#10B981",
      celluleId: celluleWeb.id,
      createdById: pilote.id,
    },
  });

  // Create tasks
  await prisma.task.create({
    data: {
      title: "Créer le composant header",
      description: "Développer le header principal de l'application",
      color: "#F59E0B",
      status: "FAIT",
      segmentId: segmentFrontend.id,
      createdById: pilote.id,
      assignees: {
        create: {
          userId: responsable.id,
        },
      },
    },
  });

  await prisma.task.create({
    data: {
      title: "Implémenter l'authentification",
      description: "Système de login/logout pour les utilisateurs",
      color: "#EF4444",
      status: "NON_FAIT",
      segmentId: segmentBackend.id,
      createdById: pilote.id,
      assignees: {
        create: {
          userId: responsable.id,
        },
      },
    },
  });

  // Assign users to cellules
  await prisma.celluleUser.create({
    data: {
      celluleId: celluleWeb.id,
      userId: pilote.id,
    },
  });

  await prisma.celluleUser.create({
    data: {
      celluleId: celluleWeb.id,
      userId: responsable.id,
    },
  });

  await prisma.celluleUser.create({
    data: {
      celluleId: celluleWeb.id,
      userId: utilisateur.id,
    },
  });

  console.log("✅ Base de données initialisée avec succès!");
  console.log("📧 Comptes de test créés:");
  console.log("   Admin: <EMAIL> / password123");
  console.log("   Pilote: <EMAIL> / password123");
  console.log("   Responsable: <EMAIL> / password123");
  console.log("   Utilisateur: <EMAIL> / password123");
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
