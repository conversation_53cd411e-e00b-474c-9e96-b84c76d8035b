import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const updateTaskSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  priority: z.enum(["URGENT", "NORMAL", "NOT_IMPORTANT"]),
  segmentId: z.string(),
  assigneeIds: z.array(z.string()),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const body = await request.json();
    console.log("Update request body:", body);

    const { title, description, color, priority, segmentId, assigneeIds } =
      updateTaskSchema.parse(body);

    console.log("Parsed update data:", {
      title,
      description,
      color,
      priority,
      segmentId,
      assigneeIds,
    });

    // Verify that the segment exists
    const segment = await prisma.segment.findUnique({
      where: { id: segmentId },
    });

    if (!segment) {
      return NextResponse.json(
        { error: "Segment non trouvé" },
        { status: 404 }
      );
    }

    // Verify that all assignee users exist and have the correct role
    if (assigneeIds.length > 0) {
      const users = await prisma.user.findMany({
        where: {
          id: { in: assigneeIds },
          role: "RESPONSABLE_TACHE",
        },
      });

      if (users.length !== assigneeIds.length) {
        return NextResponse.json(
          {
            error:
              "Un ou plusieurs utilisateurs assignés sont invalides ou n'ont pas le rôle RESPONSABLE_TACHE",
          },
          { status: 400 }
        );
      }
    }

    const task = await prisma.task.update({
      where: { id: params.id },
      data: {
        title,
        description,
        color,
        priority,
        segmentId,
        assignees: {
          deleteMany: {},
          create: assigneeIds.map((userId) => ({
            userId,
          })),
        },
      },
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              },
            },
          },
        },
        createdBy: {
          select: {
            name: true,
            email: true,
          },
        },
        assignees: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        kpis: {
          include: {
            createdBy: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({ task });
  } catch (error) {
    console.error("Error updating task:", error);
    return NextResponse.json(
      {
        error: "Erreur lors de la mise à jour de la tâche",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    await prisma.task.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: "Tâche supprimée avec succès" });
  } catch (error) {
    return NextResponse.json(
      { error: "Erreur lors de la suppression de la tâche" },
      { status: 500 }
    );
  }
}
