import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    // Get all users who have exchanged messages with the current user
    const sentMessages = await prisma.message.findMany({
      where: {
        senderId: session.user.id,
      },
      select: {
        receiverId: true,
      },
      distinct: ["receiverId"],
    });

    const receivedMessages = await prisma.message.findMany({
      where: {
        receiverId: session.user.id,
      },
      select: {
        senderId: true,
      },
      distinct: ["senderId"],
    });

    const userIds = [
      ...sentMessages.map((m) => m.receiverId),
      ...receivedMessages.map((m) => m.senderId),
    ];

    const uniqueUserIds = Array.from(new Set(userIds));

    // Get conversation details for each user
    const conversations = await Promise.all(
      uniqueUserIds.map(async (userId) => {
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true,
          },
        });

        if (!user) return null;

        // Get last message between users
        const lastMessage = await prisma.message.findFirst({
          where: {
            OR: [
              { senderId: session.user.id, receiverId: userId },
              { senderId: userId, receiverId: session.user.id },
            ],
          },
          orderBy: { createdAt: "desc" },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
                role: true,
              },
            },
            receiver: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
                role: true,
              },
            },
          },
        });

        // Count unread messages
        const unreadCount = await prisma.message.count({
          where: {
            senderId: userId,
            receiverId: session.user.id,
            // In a real app, you'd have a 'read' field
          },
        });

        return {
          user,
          lastMessage,
          unreadCount: 0, // Simplified for now
        };
      })
    );

    const validConversations = conversations.filter(
      (conv): conv is NonNullable<typeof conv> => conv !== null
    );

    // Sort by last message date
    validConversations.sort((a, b) => {
      if (!a.lastMessage && !b.lastMessage) return 0;
      if (!a.lastMessage) return 1;
      if (!b.lastMessage) return -1;
      return (
        new Date(b.lastMessage.createdAt).getTime() -
        new Date(a.lastMessage.createdAt).getTime()
      );
    });

    return NextResponse.json({ conversations: validConversations });
  } catch (error) {
    return NextResponse.json(
      { error: "Erreur lors du chargement des conversations" },
      { status: 500 }
    );
  }
}
