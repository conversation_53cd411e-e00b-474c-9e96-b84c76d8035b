import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const cellules = await prisma.cellule.findMany({
      where: {
        users: {
          some: {
            userId: session.user.id,
          }
        }
      },
      select: {
        id: true,
        title: true,
        color: true,
      },
      orderBy: {
        title: 'asc',
      },
    })

    return NextResponse.json({ cellules })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des cellules' },
      { status: 500 }
    )
  }
}