# ENCG Agadir - Plateforme de Gestion de Projet

Une application web complète de gestion de projet développée pour l'ENCG Agadir avec un système de rôles avancé et des fonctionnalités collaboratives.

## 🎯 Fonctionnalités Principales

### Système de Rôles
- **Admin** : Accès complet, gestion des utilisateurs et rôles
- **Pilote de Cellule** : Création de segments/tâches, assignation
- **Responsable de Tâche** : Gestion des tâches assignées
- **Utilisateur** : Accès consultation et messagerie

### Fonctionnalités Clés
- Dashboards personnalisés selon les rôles
- Gestion des segments et tâches
- Système de KPIs manuels
- Messagerie intégrée
- Interface responsive en français
- Authentification sécurisée

## 🛠️ Stack Technique

- **Frontend** : Next.js 14 (App Router), TypeScript
- **Base de données** : PostgreSQL avec Prisma ORM
- **Hébergement DB** : Neon.tech
- **UI** : Shadcn/ui avec palette bleu/violet
- **Authentification** : NextAuth.js
- **Styling** : Tailwind CSS

## 🚀 Installation

1. **Cloner le projet**
   ```bash
   git clone <repository-url>
   cd encg-project-management
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Configuration de la base de données**
   
   Créez un compte sur [Neon.tech](https://neon.tech) et obtenez votre URL de connexion.
   
   Copiez `.env.example` vers `.env.local` :
   ```bash
   cp .env.example .env.local
   ```
   
   Mise à jour de `.env.local` :
   ```env
   DATABASE_URL="************************************************************************************"
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"
   ```

4. **Initialiser la base de données**
   ```bash
   npx prisma migrate dev --name init
   npx prisma generate
   node scripts/seed.js
   ```

5. **Lancer l'application**
   ```bash
   npm run dev
   ```

## 👥 Comptes de Test

| Rôle | Email | Mot de passe |
|------|-------|--------------|
| Admin | <EMAIL> | password123 |
| Pilote | <EMAIL> | password123 |
| Responsable | <EMAIL> | password123 |
| Utilisateur | <EMAIL> | password123 |

## 📋 Structure des Données

### Modèles Principaux
- **User** : Utilisateurs avec rôles
- **Cellule** : Groupes de projet
- **Segment** : Divisions de cellules
- **Task** : Tâches assignables
- **KPI** : Indicateurs de performance
- **Message** : Système de messagerie

### Relations
- Utilisateurs ↔ Cellules (Many-to-Many)
- Cellules → Segments (One-to-Many)
- Segments → Tâches (One-to-Many)
- Tâches ↔ Utilisateurs (Many-to-Many)
- Tâches → KPIs (One-to-Many)

## 🎨 Design System

### Palette de Couleurs
- **Primaire** : Bleu (#3B82F6)
- **Secondaire** : Violet (#8B5CF6)
- **Accent** : Cyan (#06B6D4)
- **Success** : Vert (#10B981)
- **Warning** : Orange (#F59E0B)
- **Error** : Rouge (#EF4444)

### Composants UI
- Utilisation exclusive de shadcn/ui
- Design responsive mobile-first
- Animations et micro-interactions
- Indicateurs de progression visuels

## 📖 Navigation

### Admin
- `/admin` - Dashboard principal
- `/admin/utilisateurs` - Gestion utilisateurs
- `/admin/responsables` - Gestion responsables
- `/admin/cellules` - Gestion cellules
- `/admin/taches` - Vue globale tâches
- `/admin/kpis` - Suivi KPIs

### Pilote de Cellule
- `/pilote` - Dashboard pilote
- `/pilote/segments` - Gestion segments
- `/pilote/taches` - Gestion tâches
- `/pilote/progression` - Suivi progression

### Responsable de Tâche
- `/responsable` - Dashboard responsable
- `/responsable/taches` - Mes tâches

### Utilisateur
- `/utilisateur` - Dashboard utilisateur

### Commun
- `/chat` - Messagerie
- `/parametres` - Paramètres profil
- `/auth/connexion` - Connexion
- `/auth/inscription` - Inscription

## 🔧 Commandes Utiles

```bash
# Développement
npm run dev

# Build production
npm run build

# Prisma
npx prisma studio          # Interface graphique DB
npx prisma migrate reset    # Reset DB
npx prisma db push         # Push schema sans migration

# Seed
node scripts/seed.js       # Initialiser données test
```

## 🚦 Déploiement

1. **Build de production**
   ```bash
   npm run build
   ```

2. **Variables d'environnement de production**
   - Configurer `DATABASE_URL` pour Neon.tech
   - Générer `NEXTAUTH_SECRET` sécurisé
   - Définir `NEXTAUTH_URL` de production

3. **Migration de production**
   ```bash
   npx prisma migrate deploy
   ```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche (`git checkout -b feature/amazing-feature`)
3. Commit (`git commit -m 'Add amazing feature'`)
4. Push (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est développé pour l'ENCG Agadir.