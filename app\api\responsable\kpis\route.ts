import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createKpiSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  value: z.number(),
  taskId: z.string(),
})

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const kpis = await prisma.kpi.findMany({
      where: {
        createdById: session.user.id,
      },
      include: {
        task: {
          include: {
            segment: {
              select: {
                title: true,
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json({ kpis })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des KPIs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, value, taskId } = createKpiSchema.parse(body)

    // Verify that the task is assigned to the current user
    const task = await prisma.task.findFirst({
      where: {
        id: taskId,
        assignees: {
          some: {
            userId: session.user.id,
          }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        { error: 'Tâche non trouvée ou non assignée' },
        { status: 404 }
      )
    }

    const kpi = await prisma.kpi.create({
      data: {
        title,
        description,
        value,
        taskId,
        createdById: session.user.id,
      },
      include: {
        task: {
          include: {
            segment: {
              select: {
                title: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json({ kpi })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la création du KPI' },
      { status: 500 }
    )
  }
}