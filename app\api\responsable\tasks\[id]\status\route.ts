import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateStatusSchema = z.object({
  status: z.enum(['FAIT', 'NON_FAIT']),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { status } = updateStatusSchema.parse(body)

    // Verify that the task is assigned to the current user
    const task = await prisma.task.findFirst({
      where: {
        id: params.id,
        assignees: {
          some: {
            userId: session.user.id,
          }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        { error: 'Tâche non trouvée ou non assignée' },
        { status: 404 }
      )
    }

    const updatedTask = await prisma.task.update({
      where: { id: params.id },
      data: { status },
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              }
            }
          }
        },
        kpis: true,
      }
    })

    return NextResponse.json({ task: updatedTask })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la mise à jour du statut' },
      { status: 500 }
    )
  }
}