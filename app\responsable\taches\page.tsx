"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  CheckSquare,
  Plus,
  BarChart3,
  Clock,
  AlertCircle,
  Check,
  X,
} from "lucide-react";

interface Task {
  id: string;
  title: string;
  description: string;
  color: string;
  status: "FAIT" | "NON_FAIT";
  priority: "URGENT" | "NORMAL" | "NOT_IMPORTANT";
  createdAt: string;
  segment: {
    title: string;
    cellule: {
      title: string;
    };
  };
  kpis: {
    id: string;
    title: string;
    description: string;
    value: number;
    createdAt: string;
  }[];
}

const priorityColors = {
  URGENT: "bg-red-100 text-red-800",
  NORMAL: "bg-blue-100 text-blue-800",
  NOT_IMPORTANT: "bg-gray-100 text-gray-800",
};

const priorityLabels = {
  URGENT: "Urgent",
  NORMAL: "Normal",
  NOT_IMPORTANT: "Faible",
};

export default function ResponsableTachesPage() {
  const { data: session } = useSession();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isKpiDialogOpen, setIsKpiDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");

  // KPI form state
  const [kpiTitle, setKpiTitle] = useState("");
  const [kpiDescription, setKpiDescription] = useState("");
  const [kpiValue, setKpiValue] = useState("");

  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      const response = await fetch("/api/responsable/tasks");
      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks);
      }
    } catch (error) {
      setError("Erreur lors du chargement des tâches");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (
    taskId: string,
    newStatus: "FAIT" | "NON_FAIT"
  ) => {
    try {
      const response = await fetch(`/api/responsable/tasks/${taskId}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setMessage(
          `Tâche marquée comme ${
            newStatus === "FAIT" ? "terminée" : "non terminée"
          }`
        );
        fetchTasks();
      } else {
        setError("Erreur lors de la mise à jour du statut");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const handleAddKpi = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTask) return;

    setError("");
    setMessage("");

    try {
      const response = await fetch("/api/responsable/kpis", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: kpiTitle,
          description: kpiDescription,
          value: parseFloat(kpiValue),
          taskId: selectedTask.id,
        }),
      });

      if (response.ok) {
        setMessage("KPI ajouté avec succès");
        setIsKpiDialogOpen(false);
        resetKpiForm();
        fetchTasks();
      } else {
        const data = await response.json();
        setError(data.error || "Erreur lors de la création du KPI");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const resetKpiForm = () => {
    setKpiTitle("");
    setKpiDescription("");
    setKpiValue("");
    setSelectedTask(null);
  };

  const openKpiDialog = (task: Task) => {
    setSelectedTask(task);
    resetKpiForm();
    setIsKpiDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    return status === "FAIT" ? (
      <Badge className="bg-green-100 text-green-800">Terminée</Badge>
    ) : (
      <Badge variant="secondary">En cours</Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    return (
      <Badge
        className={priorityColors[priority as keyof typeof priorityColors]}
      >
        {priorityLabels[priority as keyof typeof priorityLabels]}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["RESPONSABLE_TACHE"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["RESPONSABLE_TACHE"]}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Mes Tâches</h1>
          <p className="text-muted-foreground">
            Gérez vos tâches assignées et ajoutez des KPIs
          </p>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-green-600">
              {message}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Tasks Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tâches
              </CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tasks.length}</div>
              <p className="text-xs text-muted-foreground">Tâches assignées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Terminées</CardTitle>
              <Check className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.filter((task) => task.status === "FAIT").length}
              </div>
              <p className="text-xs text-muted-foreground">Complétées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Cours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.filter((task) => task.status === "NON_FAIT").length}
              </div>
              <p className="text-xs text-muted-foreground">À terminer</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">KPIs Totaux</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {tasks.reduce((total, task) => total + task.kpis.length, 0)}
              </div>
              <p className="text-xs text-muted-foreground">Indicateurs créés</p>
            </CardContent>
          </Card>
        </div>

        {/* Tasks List */}
        <div className="space-y-4">
          {tasks.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Aucune tâche assignée pour le moment
                </p>
              </CardContent>
            </Card>
          ) : (
            tasks.map((task) => (
              <Card key={task.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: task.color }}
                      ></div>
                      <CardTitle className="text-lg">{task.title}</CardTitle>
                      {getStatusBadge(task.status)}
                      {getPriorityBadge(task.priority)}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openKpiDialog(task)}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        KPI
                      </Button>
                      {task.status === "NON_FAIT" ? (
                        <Button
                          size="sm"
                          onClick={() => handleStatusChange(task.id, "FAIT")}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Terminer
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleStatusChange(task.id, "NON_FAIT")
                          }
                        >
                          <X className="h-4 w-4 mr-1" />
                          Rouvrir
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {task.description && (
                    <p className="text-muted-foreground mb-4">
                      {task.description}
                    </p>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-4">
                    <span>Segment: {task.segment.title}</span>
                    <span>Cellule: {task.segment.cellule.title}</span>
                    <span>
                      Créée le{" "}
                      {new Date(task.createdAt).toLocaleDateString("fr-FR")}
                    </span>
                  </div>

                  {/* KPIs Display */}
                  {task.kpis.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium mb-2 block">
                        KPIs associés:
                      </Label>
                      <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                        {task.kpis.map((kpi) => (
                          <div
                            key={kpi.id}
                            className="p-3 border rounded-lg bg-accent/50"
                          >
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium text-sm">
                                {kpi.title}
                              </h4>
                              <Badge
                                variant="outline"
                                className="text-lg font-bold"
                              >
                                {kpi.value}
                              </Badge>
                            </div>
                            {kpi.description && (
                              <p className="text-xs text-muted-foreground mb-1">
                                {kpi.description}
                              </p>
                            )}
                            <p className="text-xs text-muted-foreground">
                              Créé le{" "}
                              {new Date(kpi.createdAt).toLocaleDateString(
                                "fr-FR"
                              )}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Add KPI Dialog */}
        <Dialog open={isKpiDialogOpen} onOpenChange={setIsKpiDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Ajouter un KPI</DialogTitle>
              <DialogDescription>
                Ajoutez un nouvel indicateur de performance pour la tâche "
                {selectedTask?.title}"
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleAddKpi} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="kpiTitle">Titre du KPI</Label>
                <Input
                  id="kpiTitle"
                  value={kpiTitle}
                  onChange={(e) => setKpiTitle(e.target.value)}
                  placeholder="Ex: Taux de completion"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="kpiDescription">Description</Label>
                <Textarea
                  id="kpiDescription"
                  value={kpiDescription}
                  onChange={(e) => setKpiDescription(e.target.value)}
                  placeholder="Description détaillée du KPI"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="kpiValue">Valeur</Label>
                <Input
                  id="kpiValue"
                  type="number"
                  step="0.01"
                  value={kpiValue}
                  onChange={(e) => setKpiValue(e.target.value)}
                  placeholder="Ex: 85.5"
                  required
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsKpiDialogOpen(false)}
                >
                  Annuler
                </Button>
                <Button type="submit">Ajouter KPI</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
