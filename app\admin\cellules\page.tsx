"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Building2,
  Plus,
  Edit,
  Trash2,
  Users,
  CheckSquare,
  Eye,
} from "lucide-react";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface Cellule {
  id: string;
  title: string;
  description: string;
  color: string;
  createdAt: string;
  users: {
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
  }[];
  _count: {
    segments: number;
  };
}

const colorOptions = [
  { value: "#3B82F6", label: "Bleu", class: "bg-blue-500" },
  { value: "#8B5CF6", label: "Violet", class: "bg-violet-500" },
  { value: "#06B6D4", label: "Cyan", class: "bg-cyan-500" },
  { value: "#10B981", label: "Vert", class: "bg-emerald-500" },
  { value: "#F59E0B", label: "Orange", class: "bg-amber-500" },
  { value: "#EF4444", label: "Rouge", class: "bg-red-500" },
];

export default function AdminCellulesPage() {
  const [cellules, setCellules] = useState<Cellule[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCellule, setEditingCellule] = useState<Cellule | null>(null);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState("#3B82F6");
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [expandedCellules, setExpandedCellules] = useState<Set<string>>(
    new Set()
  );

  useEffect(() => {
    fetchCellules();
    fetchUsers();
  }, []);

  const fetchCellules = async () => {
    try {
      const response = await fetch("/api/admin/cellules");
      if (response.ok) {
        const data = await response.json();
        setCellules(data.cellules);
      }
    } catch (error) {
      setError("Erreur lors du chargement des cellules");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/admin/users");
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs");
    }
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setColor("#3B82F6");
    setSelectedUserIds([]);
    setEditingCellule(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");

    const celluleData = {
      title,
      description,
      color,
      userIds: selectedUserIds,
    };

    try {
      const url = editingCellule
        ? `/api/admin/cellules/${editingCellule.id}`
        : "/api/admin/cellules";

      const method = editingCellule ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(celluleData),
      });

      if (response.ok) {
        setMessage(
          editingCellule
            ? "Cellule mise à jour avec succès"
            : "Cellule créée avec succès"
        );
        setIsDialogOpen(false);
        resetForm();
        fetchCellules();
      } else {
        const data = await response.json();
        setError(data.error || "Erreur lors de la sauvegarde");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const handleEdit = (cellule: Cellule) => {
    setEditingCellule(cellule);
    setTitle(cellule.title);
    setDescription(cellule.description);
    setColor(cellule.color);
    setSelectedUserIds(cellule.users.map((u) => u.user.id));
    setIsDialogOpen(true);
  };

  const handleDelete = async (celluleId: string) => {
    if (
      !confirm(
        "Êtes-vous sûr de vouloir supprimer cette cellule ? Tous les segments et tâches associés seront également supprimés."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/cellules/${celluleId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setMessage("Cellule supprimée avec succès");
        fetchCellules();
      } else {
        setError("Erreur lors de la suppression");
      }
    } catch (error) {
      setError("Une erreur est survenue");
    }
  };

  const handleUserSelection = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUserIds([...selectedUserIds, userId]);
    } else {
      setSelectedUserIds(selectedUserIds.filter((id) => id !== userId));
    }
  };

  const toggleCelluleExpansion = (celluleId: string) => {
    const newExpanded = new Set(expandedCellules);
    if (newExpanded.has(celluleId)) {
      newExpanded.delete(celluleId);
    } else {
      newExpanded.add(celluleId);
    }
    setExpandedCellules(newExpanded);
  };

  const openCreateDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const getRoleBadge = (role: string) => {
    const roleColors = {
      ADMIN: "bg-red-100 text-red-800",
      PILOTE_CELLULE: "bg-blue-100 text-blue-800",
      RESPONSABLE_TACHE: "bg-green-100 text-green-800",
      UTILISATEUR: "bg-gray-100 text-gray-800",
    };

    const roleLabels = {
      ADMIN: "Admin",
      PILOTE_CELLULE: "Pilote",
      RESPONSABLE_TACHE: "Responsable",
      UTILISATEUR: "Utilisateur",
    };

    return (
      <Badge className={roleColors[role as keyof typeof roleColors]}>
        {roleLabels[role as keyof typeof roleLabels]}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["ADMIN"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["ADMIN"]}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Gestion des Cellules
            </h1>
            <p className="text-muted-foreground">
              Créez et gérez les cellules de projet, assignez les utilisateurs
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Cellule
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCellule
                    ? "Modifier la cellule"
                    : "Créer une nouvelle cellule"}
                </DialogTitle>
                <DialogDescription>
                  {editingCellule
                    ? "Modifiez les informations de cette cellule"
                    : "Ajoutez une nouvelle cellule et assignez les utilisateurs"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre de la cellule</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Ex: Développement Web"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Description détaillée de la cellule"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Couleur</Label>
                  <Select value={color} onValueChange={setColor}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <div
                              className={`w-4 h-4 rounded-full ${option.class}`}
                            ></div>
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Utilisateurs assignés</Label>
                  <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                    {users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center space-x-2 py-1"
                      >
                        <Checkbox
                          id={user.id}
                          checked={selectedUserIds.includes(user.id)}
                          onCheckedChange={(checked) =>
                            handleUserSelection(user.id, checked as boolean)
                          }
                        />
                        <Label htmlFor={user.id} className="text-sm flex-1">
                          {user.name} ({user.email})
                        </Label>
                        {getRoleBadge(user.role)}
                      </div>
                    ))}
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingCellule ? "Mettre à jour" : "Créer"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-green-600">
              {message}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Cellules Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Cellules
              </CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{cellules.length}</div>
              <p className="text-xs text-muted-foreground">Cellules créées</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Utilisateurs Assignés
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {cellules.reduce(
                  (total, cellule) => total + cellule.users.length,
                  0
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Assignations totales
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Segments Totaux
              </CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {cellules.reduce(
                  (total, cellule) => total + cellule._count.segments,
                  0
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Dans toutes les cellules
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Moyenne Utilisateurs
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {cellules.length > 0
                  ? Math.round(
                      cellules.reduce(
                        (total, cellule) => total + cellule.users.length,
                        0
                      ) / cellules.length
                    )
                  : 0}
              </div>
              <p className="text-xs text-muted-foreground">Par cellule</p>
            </CardContent>
          </Card>
        </div>

        {/* Cellules List */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Cellules</CardTitle>
            <CardDescription>
              Toutes les cellules de projet avec leurs utilisateurs assignés
            </CardDescription>
          </CardHeader>
          <CardContent>
            {cellules.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Aucune cellule créée pour le moment
                </p>
                <p className="text-sm text-muted-foreground">
                  Commencez par créer votre première cellule de projet
                </p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2">
                {cellules.map((cellule) => (
                  <Card
                    key={cellule.id}
                    className="hover:shadow-md transition-shadow"
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: cellule.color }}
                          ></div>
                          <CardTitle className="text-lg">
                            {cellule.title}
                          </CardTitle>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(cellule)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(cellule.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {cellule.description && (
                        <p className="text-sm text-muted-foreground mb-3">
                          {cellule.description}
                        </p>
                      )}

                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>{cellule.users.length} utilisateurs</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <CheckSquare className="h-4 w-4" />
                            <span>{cellule._count.segments} segments</span>
                          </span>
                        </div>

                        {cellule.users.length > 0 && (
                          <div>
                            <div className="flex items-center justify-between">
                              <Label className="text-xs font-medium text-muted-foreground">
                                Utilisateurs assignés:
                              </Label>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  toggleCelluleExpansion(cellule.id)
                                }
                                className="h-6 px-2"
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                <span className="text-xs">
                                  {expandedCellules.has(cellule.id)
                                    ? "Masquer"
                                    : "Afficher plus"}
                                </span>
                              </Button>
                            </div>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {expandedCellules.has(cellule.id)
                                ? // Detailed view with roles
                                  cellule.users.map((userCellule) => (
                                    <div
                                      key={userCellule.user.id}
                                      className="flex items-center space-x-1"
                                    >
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {userCellule.user.name}
                                      </Badge>
                                      {getRoleBadge(userCellule.user.role)}
                                    </div>
                                  ))
                                : // Simple view with names only
                                  cellule.users.map((userCellule) => (
                                    <Badge
                                      key={userCellule.user.id}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {userCellule.user.name}
                                    </Badge>
                                  ))}
                            </div>
                          </div>
                        )}

                        <div className="text-xs text-muted-foreground">
                          Créée le{" "}
                          {new Date(cellule.createdAt).toLocaleDateString(
                            "fr-FR"
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
