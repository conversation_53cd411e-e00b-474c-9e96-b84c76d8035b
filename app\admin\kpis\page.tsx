"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/layout/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { BarChart3, TrendingUp, Users, Target } from "lucide-react";

interface Kpi {
  id: string;
  title: string;
  description: string;
  value: number;
  createdAt: string;
  task: {
    title: string;
    status: string;
    priority: string;
    segment: {
      title: string;
      cellule: {
        title: string;
      };
    };
  };
  createdBy: {
    name: string;
    email: string;
  };
}

const priorityColors = {
  URGENT: "bg-red-100 text-red-800",
  NORMAL: "bg-blue-100 text-blue-800",
  NOT_IMPORTANT: "bg-gray-100 text-gray-800",
};

export default function AdminKpisPage() {
  const [kpis, setKpis] = useState<Kpi[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchKpis();
  }, []);

  const fetchKpis = async () => {
    try {
      const response = await fetch("/api/admin/kpis");
      if (response.ok) {
        const data = await response.json();
        setKpis(data.kpis);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des KPIs");
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    return status === "FAIT" ? (
      <Badge className="bg-green-100 text-green-800">Terminée</Badge>
    ) : (
      <Badge variant="secondary">En cours</Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    return (
      <Badge
        className={priorityColors[priority as keyof typeof priorityColors]}
      >
        {priority === "URGENT"
          ? "Urgent"
          : priority === "NORMAL"
          ? "Normal"
          : "Faible"}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={["ADMIN"]}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole={["ADMIN"]}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Gestion des KPIs
          </h1>
          <p className="text-muted-foreground">
            Vue d'ensemble de tous les indicateurs de performance de la
            plateforme
          </p>
        </div>

        {/* KPIs Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total KPIs</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{kpis.length}</div>
              <p className="text-xs text-muted-foreground">Indicateurs créés</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Valeur Moyenne
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {kpis.length > 0
                  ? (
                      kpis.reduce((sum, kpi) => sum + kpi.value, 0) /
                      kpis.length
                    ).toFixed(1)
                  : "0"}
              </div>
              <p className="text-xs text-muted-foreground">
                Performance globale
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Tâches Suivies
              </CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(kpis.map((kpi) => kpi.task.title)).size}
              </div>
              <p className="text-xs text-muted-foreground">Avec indicateurs</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Responsables Actifs
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(kpis.map((kpi) => kpi.createdBy.email)).size}
              </div>
              <p className="text-xs text-muted-foreground">Créateurs de KPIs</p>
            </CardContent>
          </Card>
        </div>

        {/* KPIs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des KPIs</CardTitle>
            <CardDescription>
              Tous les indicateurs de performance avec leurs détails
            </CardDescription>
          </CardHeader>
          <CardContent>
            {kpis.length === 0 ? (
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Aucun KPI trouvé</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>KPI</TableHead>
                      <TableHead>Valeur</TableHead>
                      <TableHead>Tâche</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Priorité</TableHead>
                      <TableHead>Segment</TableHead>
                      <TableHead>Cellule</TableHead>
                      <TableHead>Créé par</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {kpis.map((kpi) => (
                      <TableRow key={kpi.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{kpi.title}</div>
                            {kpi.description && (
                              <div className="text-sm text-muted-foreground">
                                {kpi.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className="text-lg font-bold"
                          >
                            {kpi.value}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{kpi.task.title}</div>
                        </TableCell>
                        <TableCell>{getStatusBadge(kpi.task.status)}</TableCell>
                        <TableCell>
                          {getPriorityBadge(kpi.task.priority)}
                        </TableCell>
                        <TableCell>{kpi.task.segment.title}</TableCell>
                        <TableCell>{kpi.task.segment.cellule.title}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {kpi.createdBy.name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {kpi.createdBy.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(kpi.createdAt).toLocaleDateString("fr-FR")}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
