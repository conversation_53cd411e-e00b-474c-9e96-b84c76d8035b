import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createSegmentSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  celluleId: z.string(),
})

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const segments = await prisma.segment.findMany({
      where: {
        createdById: session.user.id,
      },
      include: {
        cellule: {
          select: {
            id: true,
            title: true,
          }
        },
        _count: {
          select: {
            tasks: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json({ segments })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des segments' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, color, celluleId } = createSegmentSchema.parse(body)

    // Verify that the cellule exists and the user has access to it
    const cellule = await prisma.cellule.findFirst({
      where: {
        id: celluleId,
        users: {
          some: {
            userId: session.user.id,
          }
        }
      }
    })

    if (!cellule) {
      return NextResponse.json(
        { error: 'Cellule non trouvée ou accès non autorisé' },
        { status: 404 }
      )
    }

    const segment = await prisma.segment.create({
      data: {
        title,
        description,
        color,
        celluleId,
        createdById: session.user.id,
      },
      include: {
        cellule: {
          select: {
            id: true,
            title: true,
          }
        },
        _count: {
          select: {
            tasks: true,
          }
        }
      }
    })

    return NextResponse.json({ segment })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la création du segment' },
      { status: 500 }
    )
  }
}