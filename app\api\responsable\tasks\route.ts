import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const tasks = await prisma.task.findMany({
      where: {
        assignees: {
          some: {
            userId: session.user.id,
          }
        }
      },
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              }
            }
          }
        },
        kpis: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json({ tasks })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des tâches' },
      { status: 500 }
    )
  }
}