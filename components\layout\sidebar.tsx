"use client";

import { useState } from "react";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  LayoutDashboard,
  Users,
  Building2,
  CheckSquare,
  BarChart3,
  MessageCircle,
  Settings,
  LogOut,
  Menu,
  X,
} from "lucide-react";
import { ThemeToggle } from "@/components/ui/theme-toggle";

const adminNavItems = [
  { name: "Tableau de bord", href: "/admin", icon: LayoutDashboard },
  { name: "Utilisateurs", href: "/admin/utilisateurs", icon: Users },
  { name: "Cellules", href: "/admin/cellules", icon: Building2 },
  { name: "Tâ<PERSON>", href: "/admin/taches", icon: CheckSquare },
  { name: "KPIs", href: "/admin/kpis", icon: BarChart3 },
  { name: "Messages", href: "/chat", icon: MessageCircle },
  { name: "Paramètres", href: "/parametres", icon: Settings },
];

const piloteNavItems = [
  { name: "Tableau de bord", href: "/pilote", icon: LayoutDashboard },
  { name: "Segments", href: "/pilote/segments", icon: Building2 },
  { name: "Tâches", href: "/pilote/taches", icon: CheckSquare },
  { name: "Progression", href: "/pilote/progression", icon: BarChart3 },
  { name: "Messages", href: "/chat", icon: MessageCircle },
  { name: "Paramètres", href: "/parametres", icon: Settings },
];

const responsableNavItems = [
  { name: "Tableau de bord", href: "/responsable", icon: LayoutDashboard },
  { name: "Mes Tâches", href: "/responsable/taches", icon: CheckSquare },
  { name: "Mes KPIs", href: "/responsable/kpis", icon: BarChart3 },
  { name: "Messages", href: "/chat", icon: MessageCircle },
  { name: "Paramètres", href: "/parametres", icon: Settings },
];

const userNavItems = [
  { name: "Tableau de bord", href: "/utilisateur", icon: LayoutDashboard },
  { name: "Messages", href: "/chat", icon: MessageCircle },
  { name: "Paramètres", href: "/parametres", icon: Settings },
];

export default function Sidebar() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const getNavItems = () => {
    switch (session?.user?.role) {
      case "ADMIN":
        return adminNavItems;
      case "PILOTE_CELLULE":
        return piloteNavItems;
      case "RESPONSABLE_TACHE":
        return responsableNavItems;
      default:
        return userNavItems;
    }
  };

  const navItems = getNavItems();

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-primary">Colloque ENCG</h1>
          </div>
          <ThemeToggle />
        </div>
        <p className="text-sm text-muted-foreground">Gestion des tâches</p>
      </div>

      <ScrollArea className="flex-1 p-4">
        <nav className="space-y-2">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                pathname === item.href
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:text-foreground hover:bg-accent"
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.name}</span>
            </Link>
          ))}
        </nav>
      </ScrollArea>

      <div className="p-4 border-t">
        <div className="mb-4">
          <div className="flex items-center space-x-3 mb-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={session?.user?.image || ""} />
              <AvatarFallback className="text-xs">
                {session?.user?.name?.charAt(0)?.toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {session?.user?.name}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {session?.user?.email}
              </p>
            </div>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => signOut()}
          className="w-full"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Déconnexion
        </Button>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="outline"
        size="sm"
        className="lg:hidden fixed top-4 left-4 z-50"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? (
          <X className="h-4 w-4" />
        ) : (
          <Menu className="h-4 w-4" />
        )}
      </Button>

      {/* Desktop sidebar */}
      <div className="hidden lg:block fixed inset-y-0 left-0 w-64 bg-card border-r">
        <SidebarContent />
      </div>

      {/* Mobile sidebar */}
      {isMobileOpen && (
        <div className="lg:hidden fixed inset-0 z-40">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsMobileOpen(false)}
          />
          <div className="fixed inset-y-0 left-0 w-64 bg-card border-r">
            <SidebarContent />
          </div>
        </div>
      )}
    </>
  );
}
