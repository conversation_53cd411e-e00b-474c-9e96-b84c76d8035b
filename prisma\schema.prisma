generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id             String    @id @default(cuid())
  email          String    @unique
  name           String?
  image          String?
  hashedPassword String?
  role           Role      @default(UTILISATEUR)
  emailVerified  DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  accounts         Account[]
  sessions         Session[]
  cellules         CelluleUser[]
  assignedTasks    TaskUser[]
  createdSegments  Segment[]
  createdTasks     Task[]
  sentMessages     Message[]     @relation("SentMessages")
  receivedMessages Message[]     @relation("ReceivedMessages")
  createdKpis      Kpi[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum Role {
  ADMIN
  PILOTE_CELLULE
  RESPONSABLE_TACHE
  UTILISATEUR
}

model Cellule {
  id          String   @id @default(cuid())
  title       String
  description String?
  color       String   @default("#3B82F6")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users    CelluleUser[]
  segments Segment[]

  @@map("cellules")
}

model CelluleUser {
  id        String @id @default(cuid())
  celluleId String
  userId    String

  cellule Cellule @relation(fields: [celluleId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([celluleId, userId])
  @@map("cellule_users")
}

model Segment {
  id          String   @id @default(cuid())
  title       String
  description String?
  color       String   @default("#8B5CF6")
  celluleId   String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  cellule   Cellule @relation(fields: [celluleId], references: [id], onDelete: Cascade)
  createdBy User    @relation(fields: [createdById], references: [id])
  tasks     Task[]

  @@map("segments")
}

model Task {
  id          String       @id @default(cuid())
  title       String
  description String?
  color       String       @default("#06B6D4")
  status      TaskStatus   @default(NON_FAIT)
  priority    TaskPriority @default(NORMAL)
  segmentId   String
  createdById String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  segment   Segment    @relation(fields: [segmentId], references: [id], onDelete: Cascade)
  createdBy User       @relation(fields: [createdById], references: [id])
  assignees TaskUser[]
  kpis      Kpi[]

  @@map("tasks")
}

model TaskUser {
  id     String @id @default(cuid())
  taskId String
  userId String

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([taskId, userId])
  @@map("task_users")
}

enum TaskStatus {
  FAIT
  NON_FAIT
}

enum TaskPriority {
  NORMAL
  URGENT
  NOT_IMPORTANT
}

model Kpi {
  id          String   @id @default(cuid())
  title       String
  description String?
  value       Float
  taskId      String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  task      Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  createdBy User @relation(fields: [createdById], references: [id])

  @@map("kpis")
}

model Message {
  id         String   @id @default(cuid())
  content    String
  senderId   String
  receiverId String
  createdAt  DateTime @default(now())

  sender   User @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiver User @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)

  @@map("messages")
}
