'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function HomePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/connexion')
      return
    }

    // Redirect to appropriate dashboard based on role
    switch (session.user.role) {
      case 'ADMIN':
        router.push('/admin')
        break
      case 'PILOTE_CELLULE':
        router.push('/pilote')
        break
      case 'RESPONSABLE_TACHE':
        router.push('/responsable')
        break
      default:
        router.push('/utilisateur')
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return null
}