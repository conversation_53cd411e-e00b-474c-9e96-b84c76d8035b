import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createCelluleSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  userIds: z.array(z.string()),
})

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const cellules = await prisma.cellule.findMany({
      include: {
        users: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              }
            }
          }
        },
        _count: {
          select: {
            segments: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json({ cellules })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des cellules' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, color, userIds } = createCelluleSchema.parse(body)

    const cellule = await prisma.cellule.create({
      data: {
        title,
        description,
        color,
        users: {
          create: userIds.map(userId => ({
            userId,
          }))
        }
      },
      include: {
        users: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              }
            }
          }
        },
        _count: {
          select: {
            segments: true,
          }
        }
      }
    })

    return NextResponse.json({ cellule })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la création de la cellule' },
      { status: 500 }
    )
  }
}