import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const segments = await prisma.segment.findMany({
      include: {
        cellule: {
          select: {
            title: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json({ segments })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des segments' },
      { status: 500 }
    )
  }
}