import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const createTaskSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  priority: z.enum(["URGENT", "NORMAL", "NOT_IMPORTANT"]).default("NORMAL"),
  segmentId: z.string(),
  assigneeIds: z.array(z.string()),
});

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const tasks = await prisma.task.findMany({
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              },
            },
          },
        },
        createdBy: {
          select: {
            name: true,
            email: true,
          },
        },
        assignees: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        kpis: {
          include: {
            createdBy: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ tasks });
  } catch (error) {
    return NextResponse.json(
      { error: "Erreur lors du chargement des tâches" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const body = await request.json();
    console.log("Request body:", body);

    const { title, description, color, priority, segmentId, assigneeIds } =
      createTaskSchema.parse(body);

    console.log("Parsed data:", {
      title,
      description,
      color,
      priority,
      segmentId,
      assigneeIds,
    });

    // Verify that the segment exists
    const segment = await prisma.segment.findUnique({
      where: { id: segmentId },
    });

    if (!segment) {
      return NextResponse.json(
        { error: "Segment non trouvé" },
        { status: 404 }
      );
    }

    // Verify that all assignee users exist and have the correct role
    if (assigneeIds.length > 0) {
      const users = await prisma.user.findMany({
        where: {
          id: { in: assigneeIds },
          role: "RESPONSABLE_TACHE",
        },
      });

      if (users.length !== assigneeIds.length) {
        return NextResponse.json(
          {
            error:
              "Un ou plusieurs utilisateurs assignés sont invalides ou n'ont pas le rôle RESPONSABLE_TACHE",
          },
          { status: 400 }
        );
      }
    }

    const task = await prisma.task.create({
      data: {
        title,
        description,
        color,
        priority,
        segmentId,
        createdById: session.user.id,
        assignees: {
          create: assigneeIds.map((userId) => ({
            userId,
          })),
        },
      },
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              },
            },
          },
        },
        createdBy: {
          select: {
            name: true,
            email: true,
          },
        },
        assignees: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        kpis: {
          include: {
            createdBy: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({ task });
  } catch (error) {
    console.error("Error creating task:", error);
    return NextResponse.json(
      {
        error: "Erreur lors de la création de la tâche",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
