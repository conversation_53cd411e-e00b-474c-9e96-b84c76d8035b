import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateKpiSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  value: z.number(),
  taskId: z.string(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, value, taskId } = updateKpiSchema.parse(body)

    // Verify that the KPI belongs to the current user
    const existingKpi = await prisma.kpi.findFirst({
      where: {
        id: params.id,
        createdById: session.user.id,
      }
    })

    if (!existingKpi) {
      return NextResponse.json(
        { error: 'KPI non trouvé' },
        { status: 404 }
      )
    }

    // Verify that the task is assigned to the current user
    const task = await prisma.task.findFirst({
      where: {
        id: taskId,
        assignees: {
          some: {
            userId: session.user.id,
          }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        { error: 'Tâche non trouvée ou non assignée' },
        { status: 404 }
      )
    }

    const kpi = await prisma.kpi.update({
      where: { id: params.id },
      data: {
        title,
        description,
        value,
        taskId,
      },
      include: {
        task: {
          include: {
            segment: {
              select: {
                title: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json({ kpi })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la mise à jour du KPI' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'RESPONSABLE_TACHE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    // Verify that the KPI belongs to the current user
    const existingKpi = await prisma.kpi.findFirst({
      where: {
        id: params.id,
        createdById: session.user.id,
      }
    })

    if (!existingKpi) {
      return NextResponse.json(
        { error: 'KPI non trouvé' },
        { status: 404 }
      )
    }

    await prisma.kpi.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'KPI supprimé avec succès' })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la suppression du KPI' },
      { status: 500 }
    )
  }
}