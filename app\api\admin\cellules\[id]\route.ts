import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCelluleSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  userIds: z.array(z.string()),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, color, userIds } = updateCelluleSchema.parse(body)

    const cellule = await prisma.cellule.update({
      where: { id: params.id },
      data: {
        title,
        description,
        color,
        users: {
          deleteMany: {},
          create: userIds.map(userId => ({
            userId,
          }))
        }
      },
      include: {
        users: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              }
            }
          }
        },
        _count: {
          select: {
            segments: true,
          }
        }
      }
    })

    return NextResponse.json({ cellule })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la mise à jour de la cellule' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    await prisma.cellule.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Cellule supprimée avec succès' })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors de la suppression de la cellule' },
      { status: 500 }
    )
  }
}