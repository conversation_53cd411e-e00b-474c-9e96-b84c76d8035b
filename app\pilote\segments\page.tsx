'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Building2, Plus, Edit, Trash2, CheckSquare, Users, Eye } from 'lucide-react'

interface Cellule {
  id: string
  title: string
  color: string
}

interface Segment {
  id: string
  title: string
  description: string
  color: string
  createdAt: string
  cellule: {
    id: string
    title: string
  }
  _count: {
    tasks: number
  }
  tasks?: {
    id: string
    title: string
    status: string
    assignees: {
      user: {
        name: string
      }
    }[]
    kpis: {
      id: string
      title: string
      value: number
    }[]
  }[]
}

const colorOptions = [
  { value: '#3B82F6', label: 'Bleu', class: 'bg-blue-500' },
  { value: '#8B5CF6', label: 'Violet', class: 'bg-violet-500' },
  { value: '#06B6D4', label: 'Cyan', class: 'bg-cyan-500' },
  { value: '#10B981', label: 'Vert', class: 'bg-emerald-500' },
  { value: '#F59E0B', label: 'Orange', class: 'bg-amber-500' },
  { value: '#EF4444', label: 'Rouge', class: 'bg-red-500' },
]

export default function PiloteSegmentsPage() {
  const { data: session } = useSession()
  const [segments, setSegments] = useState<Segment[]>([])
  const [cellules, setCellules] = useState<Cellule[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [editingSegment, setEditingSegment] = useState<Segment | null>(null)
  const [selectedSegment, setSelectedSegment] = useState<Segment | null>(null)
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')

  // Form state
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [color, setColor] = useState('#3B82F6')
  const [selectedCelluleId, setSelectedCelluleId] = useState('')

  useEffect(() => {
    fetchSegments()
    fetchCellules()
  }, [])

  const fetchSegments = async () => {
    try {
      const response = await fetch('/api/pilote/segments')
      if (response.ok) {
        const data = await response.json()
        setSegments(data.segments)
      }
    } catch (error) {
      setError('Erreur lors du chargement des segments')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchCellules = async () => {
    try {
      const response = await fetch('/api/pilote/cellules')
      if (response.ok) {
        const data = await response.json()
        setCellules(data.cellules)
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cellules')
    }
  }

  const resetForm = () => {
    setTitle('')
    setDescription('')
    setColor('#3B82F6')
    setSelectedCelluleId('')
    setEditingSegment(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')

    const segmentData = {
      title,
      description,
      color,
      celluleId: selectedCelluleId,
    }

    try {
      const url = editingSegment 
        ? `/api/pilote/segments/${editingSegment.id}`
        : '/api/pilote/segments'
      
      const method = editingSegment ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(segmentData),
      })

      if (response.ok) {
        setMessage(editingSegment ? 'Segment mis à jour avec succès' : 'Segment créé avec succès')
        setIsDialogOpen(false)
        resetForm()
        fetchSegments()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la sauvegarde')
      }
    } catch (error) {
      setError('Une erreur est survenue')
    }
  }

  const handleEdit = (segment: Segment) => {
    setEditingSegment(segment)
    setTitle(segment.title)
    setDescription(segment.description)
    setColor(segment.color)
    setSelectedCelluleId(segment.cellule.id)
    setIsDialogOpen(true)
  }

  const handleDelete = async (segmentId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce segment ? Toutes les tâches associées seront également supprimées.')) {
      return
    }

    try {
      const response = await fetch(`/api/pilote/segments/${segmentId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setMessage('Segment supprimé avec succès')
        fetchSegments()
      } else {
        setError('Erreur lors de la suppression')
      }
    } catch (error) {
      setError('Une erreur est survenue')
    }
  }

  const openCreateDialog = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  const openDetailsDialog = async (segment: Segment) => {
    try {
      const response = await fetch(`/api/pilote/segments/${segment.id}/details`)
      if (response.ok) {
        const data = await response.json()
        setSelectedSegment({ ...segment, tasks: data.tasks })
        setIsDetailsOpen(true)
      }
    } catch (error) {
      setError('Erreur lors du chargement des détails')
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout requiredRole={['PILOTE_CELLULE']}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout requiredRole={['PILOTE_CELLULE']}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Mes Segments</h1>
            <p className="text-muted-foreground">
              Gérez les segments de vos cellules et organisez les tâches
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Segment
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingSegment ? 'Modifier le segment' : 'Créer un nouveau segment'}
                </DialogTitle>
                <DialogDescription>
                  {editingSegment 
                    ? 'Modifiez les informations de ce segment'
                    : 'Ajoutez un nouveau segment pour organiser vos tâches'
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Titre du segment</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Ex: Développement Frontend"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Description détaillée du segment"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cellule">Cellule</Label>
                  <Select value={selectedCelluleId} onValueChange={setSelectedCelluleId} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une cellule" />
                    </SelectTrigger>
                    <SelectContent>
                      {cellules.map((cellule) => (
                        <SelectItem key={cellule.id} value={cellule.id}>
                          {cellule.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Couleur</Label>
                  <Select value={color} onValueChange={setColor}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <div className={`w-4 h-4 rounded-full ${option.class}`}></div>
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingSegment ? 'Mettre à jour' : 'Créer'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {message && (
          <Alert>
            <AlertDescription className="text-green-600">{message}</AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Segments Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Segments</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{segments.length}</div>
              <p className="text-xs text-muted-foreground">
                Segments créés
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tâches</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {segments.reduce((total, segment) => total + segment._count.tasks, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Dans tous les segments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cellules Actives</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(segments.map(segment => segment.cellule.id)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Avec segments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Moyenne Tâches</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {segments.length > 0 
                  ? Math.round(segments.reduce((total, segment) => total + segment._count.tasks, 0) / segments.length)
                  : 0
                }
              </div>
              <p className="text-xs text-muted-foreground">
                Par segment
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Segments List */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Segments</CardTitle>
            <CardDescription>
              Tous vos segments organisés par cellule
            </CardDescription>
          </CardHeader>
          <CardContent>
            {segments.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Aucun segment créé pour le moment</p>
                <p className="text-sm text-muted-foreground">
                  Commencez par créer votre premier segment pour organiser vos tâches
                </p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {segments.map((segment) => (
                  <Card key={segment.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: segment.color }}
                          ></div>
                          <CardTitle className="text-lg">{segment.title}</CardTitle>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openDetailsDialog(segment)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(segment)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(segment.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <Badge variant="outline" className="w-fit">
                        {segment.cellule.title}
                      </Badge>
                    </CardHeader>
                    <CardContent>
                      {segment.description && (
                        <p className="text-sm text-muted-foreground mb-3">
                          {segment.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center space-x-1">
                          <CheckSquare className="h-4 w-4" />
                          <span>{segment._count.tasks} tâches</span>
                        </span>
                        <span className="text-muted-foreground">
                          Créé le {new Date(segment.createdAt).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Details Modal */}
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>Détails du Segment: {selectedSegment?.title}</DialogTitle>
              <DialogDescription>
                Informations complètes du segment et ses tâches
              </DialogDescription>
            </DialogHeader>
            {selectedSegment && (
              <div className="space-y-6">
                {/* Segment Info */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium">Cellule</Label>
                    <p className="text-sm text-muted-foreground">{selectedSegment.cellule.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Nombre de tâches</Label>
                    <p className="text-sm text-muted-foreground">{selectedSegment._count.tasks}</p>
                  </div>
                </div>

                {selectedSegment.description && (
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground">{selectedSegment.description}</p>
                  </div>
                )}

                {/* Tasks */}
                <div>
                  <Label className="text-sm font-medium mb-3 block">Tâches du segment</Label>
                  {selectedSegment.tasks && selectedSegment.tasks.length > 0 ? (
                    <div className="space-y-3">
                      {selectedSegment.tasks.map((task) => (
                        <div key={task.id} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{task.title}</h4>
                            <Badge className={task.status === 'FAIT' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                              {task.status === 'FAIT' ? 'Terminée' : 'En cours'}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground mb-2">
                            Assignée à: {task.assignees.map(a => a.user.name).join(', ') || 'Aucun'}
                          </div>
                          {task.kpis.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                              {task.kpis.map((kpi) => (
                                <Badge key={kpi.id} variant="outline">
                                  {kpi.title}: {kpi.value}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Aucune tâche dans ce segment</p>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}