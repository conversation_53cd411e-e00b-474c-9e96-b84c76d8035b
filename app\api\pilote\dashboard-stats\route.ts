import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'PILOTE_CELLULE') {
      return NextResponse.json(
        { error: 'Non autorisé' },
        { status: 401 }
      )
    }

    // Get segments created by this pilote
    const segments = await prisma.segment.findMany({
      where: {
        createdById: session.user.id,
      },
      include: {
        tasks: {
          include: {
            assignees: {
              include: {
                user: {
                  select: { id: true }
                }
              }
            }
          }
        }
      }
    })

    const totalSegments = segments.length
    const totalTasks = segments.reduce((sum, segment) => sum + segment.tasks.length, 0)
    
    // Get unique responsables assigned to tasks
    const allAssignees = segments.flatMap(segment => 
      segment.tasks.flatMap(task => task.assignees.map(assignee => assignee.user.id))
    )
    const totalResponsables = new Set(allAssignees).size

    // Get recent activity from responsables
    const recentKpis = await prisma.kpi.findMany({
      where: {
        task: {
          createdById: session.user.id,
        }
      },
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        task: { select: { title: true } },
        createdBy: { select: { name: true } }
      }
    })

    const recentTaskUpdates = await prisma.task.findMany({
      where: {
        createdById: session.user.id,
        updatedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      take: 5,
      orderBy: { updatedAt: 'desc' },
      include: {
        assignees: {
          include: {
            user: { select: { name: true } }
          }
        }
      }
    })

    const recentActivity = [
      ...recentKpis.map(kpi => ({
        type: 'kpi_added',
        message: `Nouveau KPI "${kpi.title}" ajouté à "${kpi.task.title}"`,
        time: new Date(kpi.createdAt).toLocaleDateString('fr-FR'),
        user: kpi.createdBy.name
      })),
      ...recentTaskUpdates.map(task => ({
        type: task.status === 'FAIT' ? 'task_completed' : 'task_reopened',
        message: `Tâche "${task.title}" ${task.status === 'FAIT' ? 'terminée' : 'rouverte'}`,
        time: new Date(task.updatedAt).toLocaleDateString('fr-FR'),
        user: task.assignees[0]?.user.name || 'Inconnu'
      }))
    ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 5)
    // Recent segments with stats
    const recentSegments = segments.slice(0, 3).map(segment => {
      const tasksCount = segment.tasks.length
      const completedTasks = segment.tasks.filter(task => task.status === 'FAIT').length
      const progress = tasksCount > 0 ? Math.round((completedTasks / tasksCount) * 100) : 0
      const responsablesCount = new Set(segment.tasks.flatMap(task => 
        task.assignees.map(assignee => assignee.user.id)
      )).size

      return {
        id: segment.id,
        title: segment.title,
        tasksCount,
        responsablesCount,
        progress,
        color: segment.color
      }
    })

    // Task statistics
    const allTasks = segments.flatMap(segment => segment.tasks)
    const taskStats = {
      total: allTasks.length,
      completed: allTasks.filter(task => task.status === 'FAIT').length,
      inProgress: allTasks.filter(task => task.status === 'NON_FAIT').length
    }

    return NextResponse.json({
      totalSegments,
      totalTasks,
      totalResponsables,
      recentActivity,
      recentSegments,
      taskStats
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Erreur lors du chargement des statistiques' },
      { status: 500 }
    )
  }
}