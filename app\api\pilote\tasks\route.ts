import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

const createTaskSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  color: z.string(),
  priority: z.enum(["URGENT", "NORMAL", "NOT_IMPORTANT"]).default("NORMAL"),
  segmentId: z.string(),
  assigneeIds: z.array(z.string()),
});

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || session.user.role !== "PILOTE_CELLULE") {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const tasks = await prisma.task.findMany({
      where: {
        createdById: session.user.id,
      },
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              },
            },
          },
        },
        assignees: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            kpis: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ tasks });
  } catch (error) {
    return NextResponse.json(
      { error: "Erreur lors du chargement des tâches" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id || session.user.role !== "PILOTE_CELLULE") {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, color, priority, segmentId, assigneeIds } =
      createTaskSchema.parse(body);

    // Verify that the segment belongs to the current user
    const segment = await prisma.segment.findFirst({
      where: {
        id: segmentId,
        createdById: session.user.id,
      },
    });

    if (!segment) {
      return NextResponse.json(
        { error: "Segment non trouvé ou accès non autorisé" },
        { status: 404 }
      );
    }

    const task = await prisma.task.create({
      data: {
        title,
        description,
        color,
        priority,
        segmentId,
        createdById: session.user.id,
        assignees: {
          create: assigneeIds.map((userId) => ({
            userId,
          })),
        },
      },
      include: {
        segment: {
          include: {
            cellule: {
              select: {
                title: true,
              },
            },
          },
        },
        assignees: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            kpis: true,
          },
        },
      },
    });

    return NextResponse.json({ task });
  } catch (error) {
    return NextResponse.json(
      { error: "Erreur lors de la création de la tâche" },
      { status: 500 }
    );
  }
}
